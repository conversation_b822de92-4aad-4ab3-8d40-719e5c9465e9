<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Get Started - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">
    
    <style>
        .get-started-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            display: flex;
            align-items: center;
            padding: 40px 0;
        }
        
        .choice-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            height: 100%;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .choice-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0,0,0,0.15);
        }
        
        .choice-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            color: white;
            font-size: 2.5rem;
        }
        
        .contractor-icon {
            background: linear-gradient(135deg, #2C3E50 0%, #34495E 100%);
        }
        
        .btn-choice {
            padding: 15px 40px;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            border: none;
            width: 100%;
        }
        
        .btn-customer {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            color: white;
        }
        
        .btn-customer:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
            color: white;
        }
        
        .btn-contractor {
            background: linear-gradient(135deg, #2C3E50 0%, #34495E 100%);
            color: white;
        }
        
        .btn-contractor:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(44, 62, 80, 0.3);
            color: white;
        }
        
        .feature-list {
            text-align: left;
            margin: 30px 0;
        }
        
        .feature-list li {
            margin-bottom: 10px;
            padding-left: 25px;
            position: relative;
        }
        
        .feature-list li:before {
            content: '\f00c';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: #FF6B35;
        }
        
        .back-link {
            color: white;
            text-decoration: none;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            margin-top: 30px;
        }
        
        .back-link:hover {
            color: rgba(255, 255, 255, 0.8);
        }
    </style>
</head>

<body>
    <div class="get-started-container">
        <div class="container">
            <div class="text-center mb-5">
                <h1 class="display-4 text-white mb-3">Welcome to Brick & Click</h1>
                <p class="lead text-white mb-4">Choose how you'd like to get started with Sri Lanka's leading construction platform</p>
            </div>
            
            <div class="row g-5 justify-content-center">
                <div class="col-lg-5 col-md-6">
                    <div class="choice-card">
                        <div class="choice-icon">
                            <i class="fa fa-user"></i>
                        </div>
                        <h3 class="mb-3">I'm a Customer</h3>
                        <p class="text-muted mb-4">Looking for verified contractors for your construction project? Find the right professionals for your needs.</p>
                        
                        <ul class="feature-list list-unstyled">
                            <li>Browse verified CIDA contractors</li>
                            <li>Compare multiple quotes</li>
                            <li>Track project progress</li>
                            <li>Read authentic reviews</li>
                            <li>Secure payment system</li>
                        </ul>
                        
                        <a href="customer-signup.html" class="btn btn-choice btn-customer">
                            <i class="fa fa-user-plus me-2"></i>Sign Up as Customer
                        </a>
                        
                        <div class="mt-3">
                            <small class="text-muted">Already have an account? <a href="login.html" class="text-primary">Login here</a></small>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-5 col-md-6">
                    <div class="choice-card">
                        <div class="choice-icon contractor-icon">
                            <i class="fa fa-hard-hat"></i>
                        </div>
                        <h3 class="mb-3">I'm a Contractor</h3>
                        <p class="text-muted mb-4">Ready to grow your construction business? Join our verified contractor network and connect with customers.</p>
                        
                        <ul class="feature-list list-unstyled">
                            <li>Get CIDA verification badge</li>
                            <li>Receive quality project leads</li>
                            <li>Showcase your portfolio</li>
                            <li>Manage quotes and projects</li>
                            <li>Build your reputation</li>
                        </ul>
                        
                        <a href="contractor-signup.html" class="btn btn-choice btn-contractor">
                            <i class="fa fa-hard-hat me-2"></i>Sign Up as Contractor
                        </a>
                        
                        <div class="mt-3">
                            <small class="text-muted">Already have an account? <a href="login.html" class="text-primary">Login here</a></small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center">
                <a href="index.html" class="back-link">
                    <i class="fa fa-arrow-left me-2"></i>Back to Home
                </a>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    
    <script>
        // Add some interactive animations
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.choice-card');
            
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
