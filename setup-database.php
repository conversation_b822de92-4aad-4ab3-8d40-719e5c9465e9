<?php
/**
 * Quick Database Setup for Brick & Click
 * This script creates the database and essential tables
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Brick & Click Database Setup</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

try {
    // Connect to MySQL (without database)
    $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    echo "<span class='success'>✓ Connected to MySQL</span><br>";
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS brick_click_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<span class='success'>✓ Database 'brick_click_db' created</span><br>";
    
    // Connect to the new database
    $pdo = new PDO("mysql:host=localhost;dbname=brick_click_db;charset=utf8mb4", 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    // Create essential tables
    $tables = [
        // Users table
        "CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            user_type ENUM('customer', 'contractor', 'admin') NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'active',
            email_verified BOOLEAN DEFAULT FALSE,
            email_verification_token VARCHAR(255),
            password_reset_token VARCHAR(255),
            password_reset_expires DATETIME,
            last_login DATETIME,
            preferred_language VARCHAR(5) DEFAULT 'en',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // Districts table
        "CREATE TABLE IF NOT EXISTS districts (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            name_si VARCHAR(100),
            name_ta VARCHAR(100),
            province VARCHAR(50) NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        // Customer profiles table
        "CREATE TABLE IF NOT EXISTS customer_profiles (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            district VARCHAR(50),
            address TEXT,
            date_of_birth DATE,
            gender ENUM('male', 'female', 'other'),
            profile_picture VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )",
        
        // Activity logs table
        "CREATE TABLE IF NOT EXISTS activity_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT,
            action VARCHAR(100) NOT NULL,
            entity_type VARCHAR(50),
            entity_id INT,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )"
    ];
    
    foreach ($tables as $sql) {
        $pdo->exec($sql);
    }
    echo "<span class='success'>✓ Essential tables created</span><br>";
    
    // Insert sample districts
    $districts = [
        ['Colombo', 'කොළඹ', 'கொழும்பு', 'Western'],
        ['Gampaha', 'ගම්පහ', 'கம்பஹா', 'Western'],
        ['Kalutara', 'කළුතර', 'களுத்துறை', 'Western'],
        ['Kandy', 'මහනුවර', 'கண்டி', 'Central'],
        ['Matale', 'මාතලේ', 'மாத்தளை', 'Central'],
        ['Galle', 'ගාල්ල', 'காலி', 'Southern'],
        ['Kurunegala', 'කුරුණෑගල', 'குருணாகல்', 'North Western'],
        ['Anuradhapura', 'අනුරාධපුර', 'அனுராதபுரம்', 'North Central']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO districts (name, name_si, name_ta, province) VALUES (?, ?, ?, ?)");
    foreach ($districts as $district) {
        $stmt->execute($district);
    }
    echo "<span class='success'>✓ Sample districts inserted</span><br>";
    
    // Create admin user
    $adminEmail = '<EMAIL>';
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (email, password_hash, user_type, first_name, last_name, status, email_verified) VALUES (?, ?, 'admin', 'System', 'Administrator', 'active', 1)");
    $stmt->execute([$adminEmail, $adminPassword]);
    
    if ($stmt->rowCount() > 0) {
        echo "<span class='success'>✓ Admin user created (<EMAIL> / admin123)</span><br>";
    } else {
        echo "<span class='info'>ℹ Admin user already exists</span><br>";
    }
    
    // Create sample customer
    $customerEmail = '<EMAIL>';
    $customerPassword = password_hash('admin123', PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (email, password_hash, user_type, first_name, last_name, status, email_verified) VALUES (?, ?, 'customer', 'John', 'Doe', 'active', 1)");
    $stmt->execute([$customerEmail, $customerPassword]);
    
    if ($stmt->rowCount() > 0) {
        echo "<span class='success'>✓ Sample customer created (<EMAIL> / admin123)</span><br>";
        
        // Create customer profile
        $userId = $pdo->lastInsertId();
        $stmt = $pdo->prepare("INSERT INTO customer_profiles (user_id, district) VALUES (?, 'colombo')");
        $stmt->execute([$userId]);
    } else {
        echo "<span class='info'>ℹ Sample customer already exists</span><br>";
    }
    
    echo "<br><h2>✅ Database Setup Complete!</h2>";
    echo "<p><strong>You can now test the registration form.</strong></p>";
    
    echo "<h3>Test Accounts:</h3>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> <EMAIL> / admin123</li>";
    echo "<li><strong>Customer:</strong> <EMAIL> / admin123</li>";
    echo "</ul>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Go to <a href='simple-register.html'>simple-register.html</a> to test registration</li>";
    echo "<li>Go to <a href='login.html'>login.html</a> to test login</li>";
    echo "<li>Go to <a href='admin-login.html'>admin-login.html</a> to test admin login</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<span class='error'>✗ Database Error: " . $e->getMessage() . "</span><br>";
    echo "<p><strong>Make sure XAMPP MySQL is running!</strong></p>";
} catch (Exception $e) {
    echo "<span class='error'>✗ Error: " . $e->getMessage() . "</span><br>";
}
?>
