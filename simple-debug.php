<?php
/**
 * Simple API Debug Script
 * Quick check for common issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Simple API Debug</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

// 1. Check MySQL Connection
echo "<h2>1. Database Connection</h2>";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=brick_click_db;charset=utf8mb4", 'root', '');
    echo "<span class='success'>✓ Database connected successfully</span><br>";
    
    // Check tables
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<span class='info'>Tables found: " . count($tables) . "</span><br>";
    
    if (in_array('users', $tables)) {
        echo "<span class='success'>✓ Users table exists</span><br>";
    } else {
        echo "<span class='error'>✗ Users table missing - run setup-database.php</span><br>";
    }
    
} catch (Exception $e) {
    echo "<span class='error'>✗ Database error: " . $e->getMessage() . "</span><br>";
    echo "<span class='info'>→ Make sure XAMPP MySQL is running</span><br>";
    echo "<span class='info'>→ Run setup-database.php to create database</span><br>";
}

// 2. Check API Files
echo "<h2>2. API Files</h2>";
$files = [
    'api/index.php' => 'Main API file',
    'api/config/config.php' => 'Configuration',
    'api/classes/Auth.php' => 'Authentication class',
    'api/classes/Database.php' => 'Database class'
];

foreach ($files as $file => $desc) {
    if (file_exists($file)) {
        echo "<span class='success'>✓ $desc ($file)</span><br>";
    } else {
        echo "<span class='error'>✗ Missing: $desc ($file)</span><br>";
    }
}

// 3. Test API Response
echo "<h2>3. API Response Test</h2>";
echo "<p>Testing what the API actually returns...</p>";

$apiUrl = 'http://localhost/brick-click/api/districts';
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Content-Type: application/json',
        'timeout' => 10
    ]
]);

$response = @file_get_contents($apiUrl, false, $context);

if ($response === false) {
    echo "<span class='error'>✗ Could not connect to API</span><br>";
    echo "<span class='info'>→ Make sure XAMPP Apache is running</span><br>";
    echo "<span class='info'>→ Check if URL is correct: $apiUrl</span><br>";
} else {
    echo "<span class='success'>✓ API responded</span><br>";
    echo "<h3>Response (first 500 characters):</h3>";
    echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
    
    // Check if it's JSON
    $json = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<span class='success'>✓ Valid JSON response</span><br>";
    } else {
        echo "<span class='error'>✗ Invalid JSON - API returning HTML/error page</span><br>";
        echo "<span class='error'>JSON Error: " . json_last_error_msg() . "</span><br>";
        
        if (strpos($response, 'Fatal error') !== false) {
            echo "<span class='error'>→ PHP Fatal Error detected in API</span><br>";
        }
        if (strpos($response, 'Parse error') !== false) {
            echo "<span class='error'>→ PHP Syntax Error detected in API</span><br>";
        }
    }
}

// 4. Quick Fix Suggestions
echo "<h2>4. Quick Fixes</h2>";
echo "<div style='background:#f0f8ff;padding:15px;border-radius:5px;'>";
echo "<h3>If you see errors above:</h3>";
echo "<ol>";
echo "<li><strong>Database issues:</strong> Run <a href='setup-database.php' style='color:blue;'>setup-database.php</a></li>";
echo "<li><strong>Missing files:</strong> Make sure all API files are uploaded</li>";
echo "<li><strong>PHP errors:</strong> Check XAMPP error logs</li>";
echo "<li><strong>HTML response:</strong> There's a PHP error in the API</li>";
echo "</ol>";

echo "<h3>If everything looks good:</h3>";
echo "<ol>";
echo "<li>Test registration: <a href='simple-register.html' style='color:blue;'>simple-register.html</a></li>";
echo "<li>Test login: <a href='login.html' style='color:blue;'>login.html</a></li>";
echo "<li>Test admin: <a href='admin-login.html' style='color:blue;'>admin-login.html</a></li>";
echo "</ol>";
echo "</div>";

// 5. Browser Console Test
echo "<h2>5. Browser Console Test</h2>";
echo "<p>Copy this code into your browser console (F12) to test the API:</p>";
echo "<textarea style='width:100%;height:150px;font-family:monospace;'>";
echo "// Test API connection
fetch('api/districts')
  .then(response => {
    console.log('Status:', response.status);
    return response.text();
  })
  .then(text => {
    console.log('Response:', text);
    try {
      const json = JSON.parse(text);
      console.log('JSON:', json);
    } catch(e) {
      console.error('Not JSON:', e);
    }
  })
  .catch(error => console.error('Error:', error));";
echo "</textarea>";

echo "<h2>6. Manual Registration Test</h2>";
echo "<p>If the API is working, test registration with this code:</p>";
echo "<textarea style='width:100%;height:200px;font-family:monospace;'>";
echo "// Test registration
fetch('api/auth/register', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    user_type: 'customer',
    first_name: 'Test',
    last_name: 'User',
    email: 'test' + Date.now() + '@example.com',
    phone: '0771234567',
    district: 'colombo',
    password: 'password123',
    preferred_language: 'en'
  })
})
.then(response => {
  console.log('Status:', response.status);
  return response.text();
})
.then(text => {
  console.log('Response:', text);
  try {
    const json = JSON.parse(text);
    console.log('Registration result:', json);
  } catch(e) {
    console.error('Not JSON:', e);
  }
})
.catch(error => console.error('Error:', error));";
echo "</textarea>";

?>
