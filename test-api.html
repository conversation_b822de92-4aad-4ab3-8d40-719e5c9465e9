<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - Brick & Click</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        input, select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <h1>Brick & Click API Test</h1>
    
    <div class="test-container">
        <h3>1. Test API Base URL</h3>
        <p>Testing if the API endpoint is accessible...</p>
        <button onclick="testApiBase()">Test API Base</button>
        <div id="apiBaseResult" class="result"></div>
    </div>

    <div class="test-container">
        <h3>2. Test Districts Endpoint</h3>
        <p>Testing a simple GET endpoint...</p>
        <button onclick="testDistricts()">Test Districts</button>
        <div id="districtsResult" class="result"></div>
    </div>

    <div class="test-container">
        <h3>3. Test Registration Endpoint</h3>
        <p>Testing the registration endpoint with sample data...</p>
        <div>
            <label>Email:</label>
            <input type="email" id="testEmail" value="<EMAIL>">
            <label>First Name:</label>
            <input type="text" id="testFirstName" value="Test">
            <label>Last Name:</label>
            <input type="text" id="testLastName" value="User">
        </div>
        <button onclick="testRegistration()">Test Registration</button>
        <div id="registrationResult" class="result"></div>
    </div>

    <div class="test-container">
        <h3>4. Direct API File Test</h3>
        <p>Testing direct access to API file...</p>
        <button onclick="testDirectApi()">Test Direct API</button>
        <div id="directApiResult" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'api';

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        async function testApiBase() {
            showResult('apiBaseResult', 'Testing API base URL...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const contentType = response.headers.get('content-type');
                const responseText = await response.text();
                
                const result = `Status: ${response.status}
Content-Type: ${contentType}
Response: ${responseText}`;
                
                showResult('apiBaseResult', result, response.ok ? 'success' : 'error');
                
            } catch (error) {
                showResult('apiBaseResult', `Error: ${error.message}`, 'error');
            }
        }

        async function testDistricts() {
            showResult('districtsResult', 'Testing districts endpoint...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/districts`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const contentType = response.headers.get('content-type');
                
                let result = `Status: ${response.status}
Content-Type: ${contentType}
`;
                
                if (contentType && contentType.includes('application/json')) {
                    const data = await response.json();
                    result += `JSON Response: ${JSON.stringify(data, null, 2)}`;
                } else {
                    const text = await response.text();
                    result += `Text Response: ${text}`;
                }
                
                showResult('districtsResult', result, response.ok ? 'success' : 'error');
                
            } catch (error) {
                showResult('districtsResult', `Error: ${error.message}`, 'error');
            }
        }

        async function testRegistration() {
            showResult('registrationResult', 'Testing registration endpoint...', 'info');
            
            const testData = {
                user_type: 'customer',
                first_name: document.getElementById('testFirstName').value,
                last_name: document.getElementById('testLastName').value,
                email: document.getElementById('testEmail').value,
                phone: '0771234567',
                district: 'colombo',
                password: 'testpass123',
                preferred_language: 'en'
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                const contentType = response.headers.get('content-type');
                
                let result = `Status: ${response.status}
Content-Type: ${contentType}
Request Data: ${JSON.stringify(testData, null, 2)}

`;
                
                if (contentType && contentType.includes('application/json')) {
                    const data = await response.json();
                    result += `JSON Response: ${JSON.stringify(data, null, 2)}`;
                } else {
                    const text = await response.text();
                    result += `Text Response: ${text.substring(0, 500)}${text.length > 500 ? '...' : ''}`;
                }
                
                showResult('registrationResult', result, response.ok ? 'success' : 'error');
                
            } catch (error) {
                showResult('registrationResult', `Error: ${error.message}`, 'error');
            }
        }

        async function testDirectApi() {
            showResult('directApiResult', 'Testing direct API file access...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/index.php`, {
                    method: 'GET'
                });
                
                const contentType = response.headers.get('content-type');
                const responseText = await response.text();
                
                const result = `Status: ${response.status}
Content-Type: ${contentType}
Response: ${responseText.substring(0, 500)}${responseText.length > 500 ? '...' : ''}`;
                
                showResult('directApiResult', result, response.ok ? 'success' : 'error');
                
            } catch (error) {
                showResult('directApiResult', `Error: ${error.message}`, 'error');
            }
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                testApiBase();
                setTimeout(() => testDistricts(), 1000);
            }, 500);
        });
    </script>
</body>
</html>
