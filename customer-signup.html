<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Customer Sign Up - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">

    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Open Sans', sans-serif;
        }

        .signup-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 40px 0;
        }

        .signup-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .signup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .signup-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .signup-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }

        .signup-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .signup-body {
            padding: 50px;
            background: white;
        }

        .form-floating {
            margin-bottom: 25px;
            position: relative;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(248, 249, 250, 0.8);
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
            transform: translateY(-2px);
        }

        .form-select {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(248, 249, 250, 0.8);
        }

        .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 18px 40px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 20px;
            margin-bottom: 25px;
        }

        .alert-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .back-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            color: #5a67d8;
            transform: translateX(-5px);
        }

        .feature-highlight {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
        }

        .feature-highlight h5 {
            color: #667eea;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            position: relative;
            padding-left: 30px;
        }

        .feature-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #48bb78;
            font-weight: bold;
            font-size: 18px;
        }

        .input-group-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 15px 0 0 15px;
        }

        .progress {
            height: 6px;
            border-radius: 10px;
            background: #e9ecef;
            margin-bottom: 20px;
        }

        .progress-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
        }
    </style>
</head>

<body>
    <div class="signup-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6 col-md-8">
                    <div class="signup-card">
                        <div class="signup-header">
                            <h1 class="mb-0"><i class="fa fa-home me-3"></i>Join as Customer</h1>
                            <p class="mb-0 mt-2">Find verified contractors for your dream project</p>
                        </div>

                        <div class="signup-body">
                            <!-- Feature Highlight -->
                            <div class="feature-highlight">
                                <h5><i class="fa fa-star me-2"></i>Why Choose Brick & Click?</h5>
                                <ul class="feature-list">
                                    <li>Access to 500+ verified CIDA contractors</li>
                                    <li>Compare quotes from multiple professionals</li>
                                    <li>Track your project progress in real-time</li>
                                    <li>Secure payment and quality assurance</li>
                                </ul>
                            </div>

                            <!-- Progress Bar -->
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar"></div>
                            </div>

                            <!-- Alert Messages -->
                            <div id="messageArea"></div>

                            <form id="customerSignupForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="firstName" placeholder="First Name" required>
                                            <label for="firstName">First Name</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="lastName" placeholder="Last Name" required>
                                            <label for="lastName">Last Name</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating">
                                    <input type="email" class="form-control" id="email" placeholder="Email" required>
                                    <label for="email">Email Address</label>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="tel" class="form-control" id="phone" placeholder="Phone" required>
                                            <label for="phone">Phone Number</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="district" required>
                                                <option value="">Select District</option>
                                                <option value="colombo">Colombo</option>
                                                <option value="gampaha">Gampaha</option>
                                                <option value="kalutara">Kalutara</option>
                                                <option value="kandy">Kandy</option>
                                                <option value="matale">Matale</option>
                                                <option value="galle">Galle</option>
                                                <option value="kurunegala">Kurunegala</option>
                                                <option value="anuradhapura">Anuradhapura</option>
                                                <option value="polonnaruwa">Polonnaruwa</option>
                                                <option value="badulla">Badulla</option>
                                                <option value="ratnapura">Ratnapura</option>
                                                <option value="kegalle">Kegalle</option>
                                                <option value="nuwara_eliya">Nuwara Eliya</option>
                                                <option value="hambantota">Hambantota</option>
                                                <option value="matara">Matara</option>
                                                <option value="jaffna">Jaffna</option>
                                                <option value="kilinochchi">Kilinochchi</option>
                                                <option value="mannar">Mannar</option>
                                                <option value="vavuniya">Vavuniya</option>
                                                <option value="mullaitivu">Mullaitivu</option>
                                                <option value="batticaloa">Batticaloa</option>
                                                <option value="ampara">Ampara</option>
                                                <option value="trincomalee">Trincomalee</option>
                                                <option value="puttalam">Puttalam</option>
                                                <option value="moneragala">Moneragala</option>
                                            </select>
                                            <label for="district">District</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="password" class="form-control" id="password" placeholder="Password" required>
                                            <label for="password">Password</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="password" class="form-control" id="confirmPassword" placeholder="Confirm Password" required>
                                            <label for="confirmPassword">Confirm Password</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check mb-4">
                                    <input class="form-check-input" type="checkbox" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        I agree to the <a href="#" class="text-primary">Terms and Conditions</a> and <a href="#" class="text-primary">Privacy Policy</a>
                                    </label>
                                </div>

                                <button type="submit" class="btn btn-primary w-100 py-3 mb-3">
                                    <i class="fa fa-user-plus me-2"></i>Create Customer Account
                                </button>
                            </form>

                            <div class="text-center">
                                <div class="row g-3 mb-4">
                                    <div class="col-md-6">
                                        <div class="d-grid">
                                            <a href="login.html" class="btn btn-outline-primary">
                                                <i class="fa fa-sign-in-alt me-2"></i>Already have an account?
                                            </a>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-grid">
                                            <a href="contractor-signup.html" class="btn btn-outline-secondary">
                                                <i class="fa fa-hard-hat me-2"></i>Join as Contractor
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <hr class="my-4" style="border-color: #e9ecef; border-width: 2px;">
                                <a href="index.html" class="back-link">
                                    <i class="fa fa-arrow-left me-2"></i>Back to Home
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        const API_BASE_URL = 'api';

        function showMessage(message, type = 'danger') {
            const messageArea = document.getElementById('messageArea');
            messageArea.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }

        function clearMessages() {
            document.getElementById('messageArea').innerHTML = '';
        }

        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const config = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                };

                if (data) {
                    config.body = JSON.stringify(data);
                }

                const response = await fetch(`${API_BASE_URL}/${endpoint}`, config);

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const textResponse = await response.text();
                    throw new Error('Server configuration error. Please make sure the database is set up correctly.');
                }

                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || result.message || 'An error occurred');
                }

                return result;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }

        // Progress bar functionality
        function updateProgress() {
            const form = document.getElementById('customerSignupForm');
            const inputs = form.querySelectorAll('input[required], select[required]');
            const checkbox = form.querySelector('input[type="checkbox"][required]');

            let filledInputs = 0;
            inputs.forEach(input => {
                if (input.value.trim() !== '') {
                    filledInputs++;
                }
            });

            if (checkbox && checkbox.checked) {
                filledInputs++;
            }

            const totalInputs = inputs.length + 1; // +1 for checkbox
            const progress = (filledInputs / totalInputs) * 100;

            document.getElementById('progressBar').style.width = progress + '%';
        }

        // Add event listeners to all form inputs
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('customerSignupForm');
            const inputs = form.querySelectorAll('input, select');

            inputs.forEach(input => {
                input.addEventListener('input', updateProgress);
                input.addEventListener('change', updateProgress);
            });
        });

        document.getElementById('customerSignupForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            clearMessages();

            const firstName = document.getElementById('firstName').value.trim();
            const lastName = document.getElementById('lastName').value.trim();
            const email = document.getElementById('email').value.trim();
            const phone = document.getElementById('phone').value.trim();
            const district = document.getElementById('district').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            // Validation
            if (!firstName || !lastName || !email || !phone || !district || !password || !confirmPassword) {
                showMessage('Please fill in all required fields');
                return;
            }

            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage('Please enter a valid email address');
                return;
            }

            if (password !== confirmPassword) {
                showMessage('Passwords do not match');
                return;
            }

            if (password.length < 6) {
                showMessage('Password must be at least 6 characters long');
                return;
            }

            const submitBtn = document.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Creating Account...';

            try {
                const formData = {
                    user_type: 'customer',
                    first_name: firstName,
                    last_name: lastName,
                    email: email,
                    phone: phone,
                    district: district,
                    password: password,
                    preferred_language: 'en'
                };

                const response = await apiCall('auth/register', 'POST', formData);

                if (response.success) {
                    showMessage('Account created successfully! Redirecting to your dashboard...', 'success');

                    // Store user info for dashboard
                    localStorage.setItem('userType', 'customer');
                    localStorage.setItem('userName', firstName + ' ' + lastName);
                    localStorage.setItem('userEmail', email);

                    // Redirect to customer dashboard after 2 seconds
                    setTimeout(() => {
                        window.location.href = 'customer-dashboard.html';
                    }, 2000);
                } else {
                    showMessage(response.error || 'Registration failed');
                }
            } catch (error) {
                showMessage(error.message || 'Registration failed. Please try again.');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fa fa-user-plus me-2"></i>Create Customer Account';
            }
        });
    </script>
</body>
</html>
