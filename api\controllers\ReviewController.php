<?php
/**
 * Review Controller
 * Handles reviews and ratings
 */

class ReviewController {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Submit a review
     */
    public function submitReview($customerId, $data) {
        validate_required_fields($data, [
            'contractor_id', 'project_id', 'rating', 'review_text'
        ]);
        
        // Validate rating
        if ($data['rating'] < 1 || $data['rating'] > 5) {
            throw new Exception("Rating must be between 1 and 5");
        }
        
        // Check if customer has already reviewed this contractor for this project
        $existingReview = $this->db->fetchOne(
            "SELECT id FROM reviews WHERE customer_id = :customer_id AND contractor_id = :contractor_id AND project_id = :project_id",
            [
                ':customer_id' => $customerId,
                ':contractor_id' => $data['contractor_id'],
                ':project_id' => $data['project_id']
            ]
        );
        
        if ($existingReview) {
            throw new Exception("You have already reviewed this contractor for this project");
        }
        
        // Verify customer worked with this contractor on this project
        $project = $this->db->fetchOne(
            "SELECT id FROM projects WHERE id = :project_id AND customer_id = :customer_id AND contractor_id = :contractor_id AND status = 'completed'",
            [
                ':project_id' => $data['project_id'],
                ':customer_id' => $customerId,
                ':contractor_id' => $data['contractor_id']
            ]
        );
        
        if (!$project) {
            throw new Exception("You can only review contractors for completed projects");
        }
        
        try {
            $this->db->beginTransaction();
            
            $sql = "INSERT INTO reviews (customer_id, contractor_id, project_id, rating, review_text, 
                    quality_rating, timeliness_rating, communication_rating, value_rating, 
                    would_recommend, review_photos) 
                    VALUES (:customer_id, :contractor_id, :project_id, :rating, :review_text, 
                    :quality_rating, :timeliness_rating, :communication_rating, :value_rating, 
                    :would_recommend, :review_photos)";
            
            $reviewId = $this->db->insert($sql, [
                ':customer_id' => $customerId,
                ':contractor_id' => $data['contractor_id'],
                ':project_id' => $data['project_id'],
                ':rating' => $data['rating'],
                ':review_text' => $data['review_text'],
                ':quality_rating' => $data['quality_rating'] ?? $data['rating'],
                ':timeliness_rating' => $data['timeliness_rating'] ?? $data['rating'],
                ':communication_rating' => $data['communication_rating'] ?? $data['rating'],
                ':value_rating' => $data['value_rating'] ?? $data['rating'],
                ':would_recommend' => isset($data['would_recommend']) ? (bool)$data['would_recommend'] : true,
                ':review_photos' => !empty($data['review_photos']) ? json_encode($data['review_photos']) : null
            ]);
            
            // Update contractor's average rating
            $this->updateContractorRating($data['contractor_id']);
            
            $this->db->commit();
            
            // Get contractor user ID for notification
            $contractor = $this->db->fetchOne(
                "SELECT cp.user_id, u.first_name, u.last_name 
                 FROM contractor_profiles cp 
                 JOIN users u ON cp.user_id = u.id 
                 WHERE cp.id = :contractor_id",
                [':contractor_id' => $data['contractor_id']]
            );
            
            // Get customer name for notification
            $customer = $this->db->fetchOne(
                "SELECT first_name, last_name FROM users WHERE id = :customer_id",
                [':customer_id' => $customerId]
            );
            
            // Send notification to contractor
            NotificationHelper::reviewReceived(
                $contractor['user_id'],
                $data['rating'],
                $customer['first_name'] . ' ' . $customer['last_name'],
                $reviewId
            );
            
            // Log activity
            log_activity($customerId, 'review_submitted', 'review', $reviewId, 'Review submitted for contractor');
            
            return ['review_id' => $reviewId];
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Get contractor reviews
     */
    public function getContractorReviews($contractorId, $page = 1, $limit = 10) {
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT r.*, CONCAT(u.first_name, ' ', u.last_name) as customer_name,
                p.title as project_title, p.service_category_id,
                sc.name as service_category
                FROM reviews r
                JOIN users u ON r.customer_id = u.id
                JOIN projects p ON r.project_id = p.id
                JOIN service_categories sc ON p.service_category_id = sc.id
                WHERE r.contractor_id = :contractor_id AND r.status = 'approved'
                ORDER BY r.created_at DESC
                LIMIT :limit OFFSET :offset";
        
        $reviews = $this->db->fetchAll($sql, [
            ':contractor_id' => $contractorId,
            ':limit' => $limit,
            ':offset' => $offset
        ]);
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM reviews WHERE contractor_id = :contractor_id AND status = 'approved'";
        $totalResult = $this->db->fetchOne($countSql, [':contractor_id' => $contractorId]);
        $total = $totalResult['total'];
        
        // Get rating summary
        $ratingSummary = $this->getContractorRatingSummary($contractorId);
        
        return [
            'reviews' => $reviews,
            'rating_summary' => $ratingSummary,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_items' => $total,
                'items_per_page' => $limit
            ]
        ];
    }
    
    /**
     * Get contractor rating summary
     */
    public function getContractorRatingSummary($contractorId) {
        $sql = "SELECT 
                COUNT(*) as total_reviews,
                AVG(rating) as average_rating,
                AVG(quality_rating) as average_quality,
                AVG(timeliness_rating) as average_timeliness,
                AVG(communication_rating) as average_communication,
                AVG(value_rating) as average_value,
                COUNT(CASE WHEN would_recommend = 1 THEN 1 END) as recommendations,
                COUNT(CASE WHEN rating = 5 THEN 1 END) as five_star,
                COUNT(CASE WHEN rating = 4 THEN 1 END) as four_star,
                COUNT(CASE WHEN rating = 3 THEN 1 END) as three_star,
                COUNT(CASE WHEN rating = 2 THEN 1 END) as two_star,
                COUNT(CASE WHEN rating = 1 THEN 1 END) as one_star
                FROM reviews 
                WHERE contractor_id = :contractor_id AND status = 'approved'";
        
        $summary = $this->db->fetchOne($sql, [':contractor_id' => $contractorId]);
        
        // Calculate percentages
        if ($summary['total_reviews'] > 0) {
            $summary['recommendation_percentage'] = round(($summary['recommendations'] / $summary['total_reviews']) * 100);
            $summary['five_star_percentage'] = round(($summary['five_star'] / $summary['total_reviews']) * 100);
            $summary['four_star_percentage'] = round(($summary['four_star'] / $summary['total_reviews']) * 100);
            $summary['three_star_percentage'] = round(($summary['three_star'] / $summary['total_reviews']) * 100);
            $summary['two_star_percentage'] = round(($summary['two_star'] / $summary['total_reviews']) * 100);
            $summary['one_star_percentage'] = round(($summary['one_star'] / $summary['total_reviews']) * 100);
        } else {
            $summary['recommendation_percentage'] = 0;
            $summary['five_star_percentage'] = 0;
            $summary['four_star_percentage'] = 0;
            $summary['three_star_percentage'] = 0;
            $summary['two_star_percentage'] = 0;
            $summary['one_star_percentage'] = 0;
        }
        
        // Round averages
        $summary['average_rating'] = round($summary['average_rating'], 1);
        $summary['average_quality'] = round($summary['average_quality'], 1);
        $summary['average_timeliness'] = round($summary['average_timeliness'], 1);
        $summary['average_communication'] = round($summary['average_communication'], 1);
        $summary['average_value'] = round($summary['average_value'], 1);
        
        return $summary;
    }
    
    /**
     * Respond to review (contractor response)
     */
    public function respondToReview($reviewId, $contractorUserId, $responseText) {
        // Verify contractor owns this review
        $review = $this->db->fetchOne(
            "SELECT r.*, cp.user_id
             FROM reviews r
             JOIN contractor_profiles cp ON r.contractor_id = cp.id
             WHERE r.id = :review_id AND cp.user_id = :contractor_user_id",
            [':review_id' => $reviewId, ':contractor_user_id' => $contractorUserId]
        );
        
        if (!$review) {
            throw new Exception("Review not found or access denied");
        }
        
        if ($review['contractor_response']) {
            throw new Exception("You have already responded to this review");
        }
        
        $this->db->update(
            "UPDATE reviews SET contractor_response = :response, response_date = NOW() WHERE id = :review_id",
            [':response' => $responseText, ':review_id' => $reviewId]
        );
        
        // Log activity
        log_activity($contractorUserId, 'review_response_added', 'review', $reviewId, 'Response added to review');
        
        return true;
    }
    
    /**
     * Get customer reviews (reviews given by customer)
     */
    public function getCustomerReviews($customerId, $page = 1, $limit = 10) {
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT r.*, cp.business_name, p.title as project_title,
                sc.name as service_category
                FROM reviews r
                JOIN contractor_profiles cp ON r.contractor_id = cp.id
                JOIN projects p ON r.project_id = p.id
                JOIN service_categories sc ON p.service_category_id = sc.id
                WHERE r.customer_id = :customer_id
                ORDER BY r.created_at DESC
                LIMIT :limit OFFSET :offset";
        
        $reviews = $this->db->fetchAll($sql, [
            ':customer_id' => $customerId,
            ':limit' => $limit,
            ':offset' => $offset
        ]);
        
        return $reviews;
    }
    
    /**
     * Get pending reviews for admin moderation
     */
    public function getPendingReviews($page = 1, $limit = 20) {
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT r.*, CONCAT(cu.first_name, ' ', cu.last_name) as customer_name,
                cp.business_name, p.title as project_title
                FROM reviews r
                JOIN users cu ON r.customer_id = cu.id
                JOIN contractor_profiles cp ON r.contractor_id = cp.id
                JOIN projects p ON r.project_id = p.id
                WHERE r.status = 'pending'
                ORDER BY r.created_at ASC
                LIMIT :limit OFFSET :offset";
        
        $reviews = $this->db->fetchAll($sql, [
            ':limit' => $limit,
            ':offset' => $offset
        ]);
        
        return $reviews;
    }
    
    /**
     * Moderate review (admin action)
     */
    public function moderateReview($reviewId, $adminId, $action, $reason = null) {
        $validActions = ['approve', 'reject', 'flag'];
        if (!in_array($action, $validActions)) {
            throw new Exception("Invalid moderation action");
        }
        
        $review = $this->db->fetchOne(
            "SELECT * FROM reviews WHERE id = :review_id",
            [':review_id' => $reviewId]
        );
        
        if (!$review) {
            throw new Exception("Review not found");
        }
        
        try {
            $this->db->beginTransaction();
            
            $status = $action === 'approve' ? 'approved' : ($action === 'reject' ? 'rejected' : 'flagged');
            
            $this->db->update(
                "UPDATE reviews SET status = :status, moderated_by = :admin_id, moderated_at = NOW(), moderation_reason = :reason WHERE id = :review_id",
                [
                    ':status' => $status,
                    ':admin_id' => $adminId,
                    ':reason' => $reason,
                    ':review_id' => $reviewId
                ]
            );
            
            // Update contractor rating if approved
            if ($action === 'approve') {
                $this->updateContractorRating($review['contractor_id']);
            }
            
            $this->db->commit();
            
            // Log activity
            log_activity($adminId, 'review_moderated', 'review', $reviewId, "Review {$action}ed");
            
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Report inappropriate review
     */
    public function reportReview($reviewId, $reporterId, $reason) {
        $review = $this->db->fetchOne(
            "SELECT id FROM reviews WHERE id = :review_id",
            [':review_id' => $reviewId]
        );
        
        if (!$review) {
            throw new Exception("Review not found");
        }
        
        // Check if user already reported this review
        $existingReport = $this->db->fetchOne(
            "SELECT id FROM review_reports WHERE review_id = :review_id AND reported_by = :reporter_id",
            [':review_id' => $reviewId, ':reporter_id' => $reporterId]
        );
        
        if ($existingReport) {
            throw new Exception("You have already reported this review");
        }
        
        $reportId = $this->db->insert(
            "INSERT INTO review_reports (review_id, reported_by, reason, status) VALUES (:review_id, :reported_by, :reason, 'pending')",
            [
                ':review_id' => $reviewId,
                ':reported_by' => $reporterId,
                ':reason' => $reason
            ]
        );
        
        // Log activity
        log_activity($reporterId, 'review_reported', 'review_report', $reportId, 'Review reported');
        
        return ['report_id' => $reportId];
    }
    
    /**
     * Update contractor's average rating
     */
    private function updateContractorRating($contractorId) {
        $ratingData = $this->db->fetchOne(
            "SELECT AVG(rating) as avg_rating, COUNT(*) as total_reviews 
             FROM reviews 
             WHERE contractor_id = :contractor_id AND status = 'approved'",
            [':contractor_id' => $contractorId]
        );
        
        $this->db->update(
            "UPDATE contractor_profiles SET average_rating = :avg_rating, total_reviews = :total_reviews WHERE id = :contractor_id",
            [
                ':avg_rating' => round($ratingData['avg_rating'], 2),
                ':total_reviews' => $ratingData['total_reviews'],
                ':contractor_id' => $contractorId
            ]
        );
    }
    
    /**
     * Get review statistics
     */
    public function getReviewStatistics() {
        $stats = $this->db->fetchOne(
            "SELECT 
                COUNT(*) as total_reviews,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_reviews,
                COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_reviews,
                COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_reviews,
                AVG(rating) as overall_average_rating,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as reviews_last_30_days
             FROM reviews"
        );
        
        $stats['overall_average_rating'] = round($stats['overall_average_rating'], 2);
        
        return $stats;
    }
}
?>
