<?php
/**
 * Project Controller
 * Handles project management and tracking
 */

class ProjectController {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Create project from accepted quote
     */
    public function createProjectFromQuote($quoteResponseId) {
        // Get quote response details
        $quoteResponse = $this->db->fetchOne(
            "SELECT qr.*, qreq.customer_id, qreq.title, qreq.description, qreq.project_location,
             qreq.district_id, qreq.service_category_id, qreq.project_start_date
             FROM quote_responses qr
             JOIN quote_requests qreq ON qr.quote_request_id = qreq.id
             WHERE qr.id = :response_id",
            [':response_id' => $quoteResponseId]
        );
        
        if (!$quoteResponse) {
            throw new Exception("Quote response not found");
        }
        
        try {
            $this->db->beginTransaction();
            
            // Create project
            $sql = "INSERT INTO projects (customer_id, contractor_id, quote_response_id, 
                    title, description, project_location, district_id, service_category_id,
                    total_amount, estimated_duration_weeks, start_date, status) 
                    VALUES (:customer_id, :contractor_id, :quote_response_id, 
                    :title, :description, :project_location, :district_id, :service_category_id,
                    :total_amount, :estimated_duration_weeks, :start_date, 'planning')";
            
            $projectId = $this->db->insert($sql, [
                ':customer_id' => $quoteResponse['customer_id'],
                ':contractor_id' => $quoteResponse['contractor_id'],
                ':quote_response_id' => $quoteResponseId,
                ':title' => $quoteResponse['title'],
                ':description' => $quoteResponse['description'],
                ':project_location' => $quoteResponse['project_location'],
                ':district_id' => $quoteResponse['district_id'],
                ':service_category_id' => $quoteResponse['service_category_id'],
                ':total_amount' => $quoteResponse['quoted_amount'],
                ':estimated_duration_weeks' => $quoteResponse['estimated_duration_weeks'],
                ':start_date' => $quoteResponse['project_start_date'] ?? date('Y-m-d', strtotime('+1 week'))
            ]);
            
            // Create default milestones based on project type
            $this->createDefaultMilestones($projectId, $quoteResponse);
            
            // Create initial payment schedule
            $this->createPaymentSchedule($projectId, $quoteResponse['quoted_amount']);
            
            $this->db->commit();
            
            // Log activity
            log_activity($quoteResponse['customer_id'], 'project_created', 'project', $projectId, 'Project created from accepted quote');
            
            return $projectId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Get customer projects
     */
    public function getCustomerProjects($customerId, $status = null, $page = 1, $limit = 10) {
        $offset = ($page - 1) * $limit;
        
        $conditions = ["p.customer_id = :customer_id"];
        $params = [':customer_id' => $customerId];
        
        if ($status) {
            $conditions[] = "p.status = :status";
            $params[':status'] = $status;
        }
        
        $whereClause = "WHERE " . implode(" AND ", $conditions);
        
        $sql = "SELECT p.*, cp.business_name, sc.name as service_category,
                d.name as district_name,
                CONCAT(u.first_name, ' ', u.last_name) as contractor_name,
                (SELECT COUNT(*) FROM project_milestones pm WHERE pm.project_id = p.id AND pm.status = 'completed') as completed_milestones,
                (SELECT COUNT(*) FROM project_milestones pm WHERE pm.project_id = p.id) as total_milestones
                FROM projects p
                JOIN contractor_profiles cp ON p.contractor_id = cp.id
                JOIN users u ON cp.user_id = u.id
                JOIN service_categories sc ON p.service_category_id = sc.id
                JOIN districts d ON p.district_id = d.id
                $whereClause
                ORDER BY p.created_at DESC
                LIMIT :limit OFFSET :offset";
        
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;
        
        $projects = $this->db->fetchAll($sql, $params);
        
        // Calculate progress for each project
        foreach ($projects as &$project) {
            if ($project['total_milestones'] > 0) {
                $project['progress_percentage'] = round(($project['completed_milestones'] / $project['total_milestones']) * 100);
            } else {
                $project['progress_percentage'] = 0;
            }
        }
        
        return $projects;
    }
    
    /**
     * Get contractor projects
     */
    public function getContractorProjects($contractorId, $status = null, $page = 1, $limit = 10) {
        $offset = ($page - 1) * $limit;
        
        $conditions = ["p.contractor_id = :contractor_id"];
        $params = [':contractor_id' => $contractorId];
        
        if ($status) {
            $conditions[] = "p.status = :status";
            $params[':status'] = $status;
        }
        
        $whereClause = "WHERE " . implode(" AND ", $conditions);
        
        $sql = "SELECT p.*, sc.name as service_category, d.name as district_name,
                CONCAT(u.first_name, ' ', u.last_name) as customer_name,
                u.phone as customer_phone,
                (SELECT COUNT(*) FROM project_milestones pm WHERE pm.project_id = p.id AND pm.status = 'completed') as completed_milestones,
                (SELECT COUNT(*) FROM project_milestones pm WHERE pm.project_id = p.id) as total_milestones
                FROM projects p
                JOIN users u ON p.customer_id = u.id
                JOIN service_categories sc ON p.service_category_id = sc.id
                JOIN districts d ON p.district_id = d.id
                $whereClause
                ORDER BY p.created_at DESC
                LIMIT :limit OFFSET :offset";
        
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;
        
        $projects = $this->db->fetchAll($sql, $params);
        
        // Calculate progress for each project
        foreach ($projects as &$project) {
            if ($project['total_milestones'] > 0) {
                $project['progress_percentage'] = round(($project['completed_milestones'] / $project['total_milestones']) * 100);
            } else {
                $project['progress_percentage'] = 0;
            }
        }
        
        return $projects;
    }
    
    /**
     * Get project details
     */
    public function getProjectDetails($projectId, $userId, $userType) {
        $sql = "SELECT p.*, cp.business_name, cp.cida_grade, cp.years_experience,
                sc.name as service_category, d.name as district_name,
                CONCAT(cu.first_name, ' ', cu.last_name) as customer_name,
                CONCAT(conu.first_name, ' ', conu.last_name) as contractor_name,
                cu.phone as customer_phone, conu.phone as contractor_phone,
                qr.description as quote_description, qr.terms_and_conditions,
                qr.warranty_period_months, qr.payment_terms
                FROM projects p
                JOIN contractor_profiles cp ON p.contractor_id = cp.id
                JOIN users cu ON p.customer_id = cu.id
                JOIN users conu ON cp.user_id = conu.id
                JOIN service_categories sc ON p.service_category_id = sc.id
                JOIN districts d ON p.district_id = d.id
                LEFT JOIN quote_responses qr ON p.quote_response_id = qr.id
                WHERE p.id = :project_id";
        
        // Add user-specific conditions
        if ($userType === 'customer') {
            $sql .= " AND p.customer_id = :user_id";
        } elseif ($userType === 'contractor') {
            $sql .= " AND cp.user_id = :user_id";
        }
        
        $project = $this->db->fetchOne($sql, [
            ':project_id' => $projectId,
            ':user_id' => $userId
        ]);
        
        if (!$project) {
            throw new Exception("Project not found");
        }
        
        // Get milestones
        $milestones = $this->db->fetchAll(
            "SELECT * FROM project_milestones WHERE project_id = :project_id ORDER BY milestone_order",
            [':project_id' => $projectId]
        );
        
        // Get payments
        $payments = $this->db->fetchAll(
            "SELECT * FROM project_payments WHERE project_id = :project_id ORDER BY due_date",
            [':project_id' => $projectId]
        );
        
        // Get updates
        $updates = $this->db->fetchAll(
            "SELECT pu.*, CONCAT(u.first_name, ' ', u.last_name) as author_name
             FROM project_updates pu
             JOIN users u ON pu.created_by = u.id
             WHERE pu.project_id = :project_id
             ORDER BY pu.created_at DESC",
            [':project_id' => $projectId]
        );
        
        $project['milestones'] = $milestones;
        $project['payments'] = $payments;
        $project['updates'] = $updates;
        
        // Calculate progress
        $completedMilestones = count(array_filter($milestones, function($m) { return $m['status'] === 'completed'; }));
        $totalMilestones = count($milestones);
        $project['progress_percentage'] = $totalMilestones > 0 ? round(($completedMilestones / $totalMilestones) * 100) : 0;
        
        return $project;
    }
    
    /**
     * Update milestone status
     */
    public function updateMilestoneStatus($milestoneId, $contractorUserId, $status, $notes = null) {
        // Verify contractor owns this milestone
        $milestone = $this->db->fetchOne(
            "SELECT pm.*, p.contractor_id, cp.user_id
             FROM project_milestones pm
             JOIN projects p ON pm.project_id = p.id
             JOIN contractor_profiles cp ON p.contractor_id = cp.id
             WHERE pm.id = :milestone_id AND cp.user_id = :contractor_user_id",
            [':milestone_id' => $milestoneId, ':contractor_user_id' => $contractorUserId]
        );
        
        if (!$milestone) {
            throw new Exception("Milestone not found or access denied");
        }
        
        $validStatuses = ['pending', 'in_progress', 'completed', 'on_hold'];
        if (!in_array($status, $validStatuses)) {
            throw new Exception("Invalid milestone status");
        }
        
        try {
            $this->db->beginTransaction();
            
            // Update milestone
            $updateData = [
                ':milestone_id' => $milestoneId,
                ':status' => $status,
                ':notes' => $notes
            ];
            
            $sql = "UPDATE project_milestones SET status = :status, notes = :notes";
            
            if ($status === 'completed') {
                $sql .= ", completed_at = NOW()";
            } elseif ($status === 'in_progress' && $milestone['status'] === 'pending') {
                $sql .= ", started_at = NOW()";
            }
            
            $sql .= " WHERE id = :milestone_id";
            
            $this->db->update($sql, $updateData);
            
            // Create project update
            $this->db->insert(
                "INSERT INTO project_updates (project_id, created_by, update_type, title, description) 
                 VALUES (:project_id, :created_by, 'milestone_update', :title, :description)",
                [
                    ':project_id' => $milestone['project_id'],
                    ':created_by' => $contractorUserId,
                    ':title' => "Milestone Updated: {$milestone['title']}",
                    ':description' => "Milestone status changed to {$status}" . ($notes ? ". Notes: {$notes}" : "")
                ]
            );
            
            // Check if project should be marked as completed
            if ($status === 'completed') {
                $this->checkProjectCompletion($milestone['project_id']);
            }
            
            $this->db->commit();
            
            // Get customer ID for notification
            $project = $this->db->fetchOne(
                "SELECT customer_id, title FROM projects WHERE id = :project_id",
                [':project_id' => $milestone['project_id']]
            );
            
            // Send notification to customer
            NotificationHelper::projectUpdate(
                $project['customer_id'],
                $project['title'],
                "Milestone '{$milestone['title']}' status updated to {$status}",
                $milestone['project_id']
            );
            
            // Log activity
            log_activity($contractorUserId, 'milestone_updated', 'project_milestone', $milestoneId, "Milestone status updated to {$status}");
            
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Add project update
     */
    public function addProjectUpdate($projectId, $userId, $userType, $data) {
        validate_required_fields($data, ['title', 'description']);
        
        // Verify user has access to project
        $accessSql = $userType === 'customer' 
            ? "SELECT id FROM projects WHERE id = :project_id AND customer_id = :user_id"
            : "SELECT p.id FROM projects p JOIN contractor_profiles cp ON p.contractor_id = cp.id WHERE p.id = :project_id AND cp.user_id = :user_id";
        
        $project = $this->db->fetchOne($accessSql, [':project_id' => $projectId, ':user_id' => $userId]);
        
        if (!$project) {
            throw new Exception("Project not found or access denied");
        }
        
        $sql = "INSERT INTO project_updates (project_id, created_by, update_type, title, description, attachments) 
                VALUES (:project_id, :created_by, :update_type, :title, :description, :attachments)";
        
        $updateId = $this->db->insert($sql, [
            ':project_id' => $projectId,
            ':created_by' => $userId,
            ':update_type' => $data['update_type'] ?? 'general',
            ':title' => $data['title'],
            ':description' => $data['description'],
            ':attachments' => !empty($data['attachments']) ? json_encode($data['attachments']) : null
        ]);
        
        // Log activity
        log_activity($userId, 'project_update_added', 'project_update', $updateId, 'Project update added');
        
        return ['update_id' => $updateId];
    }
    
    /**
     * Create default milestones based on project type
     */
    private function createDefaultMilestones($projectId, $quoteResponse) {
        $defaultMilestones = [
            ['title' => 'Project Planning & Permits', 'description' => 'Obtain necessary permits and finalize project plans', 'milestone_order' => 1],
            ['title' => 'Site Preparation', 'description' => 'Prepare construction site and set up temporary facilities', 'milestone_order' => 2],
            ['title' => 'Foundation Work', 'description' => 'Complete foundation and structural work', 'milestone_order' => 3],
            ['title' => 'Main Construction', 'description' => 'Complete main construction work', 'milestone_order' => 4],
            ['title' => 'Finishing Work', 'description' => 'Complete interior and exterior finishing', 'milestone_order' => 5],
            ['title' => 'Final Inspection & Handover', 'description' => 'Final quality check and project handover', 'milestone_order' => 6]
        ];
        
        foreach ($defaultMilestones as $milestone) {
            $this->db->insert(
                "INSERT INTO project_milestones (project_id, title, description, milestone_order, status) 
                 VALUES (:project_id, :title, :description, :milestone_order, 'pending')",
                [
                    ':project_id' => $projectId,
                    ':title' => $milestone['title'],
                    ':description' => $milestone['description'],
                    ':milestone_order' => $milestone['milestone_order']
                ]
            );
        }
    }
    
    /**
     * Create payment schedule
     */
    private function createPaymentSchedule($projectId, $totalAmount) {
        $payments = [
            ['description' => 'Initial Payment (20%)', 'amount' => $totalAmount * 0.20, 'due_offset_days' => 0],
            ['description' => 'Foundation Complete (30%)', 'amount' => $totalAmount * 0.30, 'due_offset_days' => 14],
            ['description' => 'Main Structure Complete (30%)', 'amount' => $totalAmount * 0.30, 'due_offset_days' => 45],
            ['description' => 'Final Payment (20%)', 'amount' => $totalAmount * 0.20, 'due_offset_days' => 75]
        ];
        
        foreach ($payments as $payment) {
            $dueDate = date('Y-m-d', strtotime("+{$payment['due_offset_days']} days"));
            
            $this->db->insert(
                "INSERT INTO project_payments (project_id, description, amount, due_date, status) 
                 VALUES (:project_id, :description, :amount, :due_date, 'pending')",
                [
                    ':project_id' => $projectId,
                    ':description' => $payment['description'],
                    ':amount' => $payment['amount'],
                    ':due_date' => $dueDate
                ]
            );
        }
    }
    
    /**
     * Check if project should be marked as completed
     */
    private function checkProjectCompletion($projectId) {
        $pendingMilestones = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM project_milestones WHERE project_id = :project_id AND status != 'completed'",
            [':project_id' => $projectId]
        );
        
        if ($pendingMilestones['count'] == 0) {
            $this->db->update(
                "UPDATE projects SET status = 'completed', completed_at = NOW() WHERE id = :project_id",
                [':project_id' => $projectId]
            );
        }
    }
}
?>
