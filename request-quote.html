<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Request Quote - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="request quote, construction, Sri Lanka" name="keywords">
    <meta content="Request Quote - Brick & Click" name="description">

    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border position-relative text-primary" style="width: 6rem; height: 6rem;" role="status"></div>
        <img class="position-absolute top-50 start-50 translate-middle" src="img/icons/icon-1.png" alt="Icon">
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5 wow fadeIn" data-wow-delay="0.1s">
        <a href="index.html" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="index.html" class="nav-item nav-link">Home</a>
                <a href="about.html" class="nav-item nav-link">About</a>
                <a href="service.html" class="nav-item nav-link">Services</a>
                <a href="search-contractors.html" class="nav-item nav-link">Find Contractors</a>
                <a href="request-quote.html" class="nav-item nav-link active">Request Quote</a>
                <a href="contact.html" class="nav-item nav-link">Contact</a>
            </div>
            <div class="d-none d-lg-flex">
                <a href="login.html" class="btn btn-primary py-2 px-4 ms-3">Login</a>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Page Header Start -->
    <div class="container-fluid page-header py-5 mb-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <h1 class="display-1 text-white animated slideInDown">Request Quote</h1>
            <nav aria-label="breadcrumb animated slideInDown">
                <ol class="breadcrumb text-uppercase mb-0">
                    <li class="breadcrumb-item"><a class="text-white" href="index.html">Home</a></li>
                    <li class="breadcrumb-item text-primary active" aria-current="page">Request Quote</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Quote Request Form Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row g-5">
                <div class="col-lg-8">
                    <div class="bg-light rounded p-5">
                        <div class="section-title section-title-sm position-relative pb-3 mb-4">
                            <h3 class="text-primary mb-0">Tell Us About Your Project</h3>
                        </div>

                        <!-- Alert Messages -->
                        <div id="alertContainer"></div>

                        <form id="quoteRequestForm">
                            <!-- Project Basic Information -->
                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">Project Information</h5>
                                </div>
                                <div class="col-12">
                                    <label for="title" class="form-label">Project Title *</label>
                                    <input type="text" class="form-control" id="title" name="title" required
                                           placeholder="e.g., Two-story house construction">
                                </div>
                                <div class="col-md-6">
                                    <label for="serviceCategory" class="form-label">Service Category *</label>
                                    <select class="form-select" id="serviceCategory" name="service_category_id" required>
                                        <option value="">Select service category</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="projectType" class="form-label">Project Type *</label>
                                    <select class="form-select" id="projectType" name="project_type" required>
                                        <option value="">Select project type</option>
                                        <option value="new_construction">New Construction</option>
                                        <option value="renovation">Renovation</option>
                                        <option value="extension">Extension</option>
                                        <option value="repair">Repair</option>
                                        <option value="maintenance">Maintenance</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <label for="description" class="form-label">Project Description *</label>
                                    <textarea class="form-control" id="description" name="description" rows="4" required
                                              placeholder="Describe your project in detail..."></textarea>
                                </div>
                            </div>

                            <!-- Location Information -->
                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">Location</h5>
                                </div>
                                <div class="col-md-6">
                                    <label for="district" class="form-label">District *</label>
                                    <select class="form-select" id="district" name="district_id" required>
                                        <option value="">Select district</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="projectLocation" class="form-label">Specific Location *</label>
                                    <input type="text" class="form-control" id="projectLocation" name="project_location" required
                                           placeholder="e.g., Colombo 07, near Town Hall">
                                </div>
                            </div>

                            <!-- Budget Information -->
                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">Budget</h5>
                                </div>
                                <div class="col-md-6">
                                    <label for="budgetMin" class="form-label">Minimum Budget (LKR)</label>
                                    <input type="number" class="form-control" id="budgetMin" name="budget_min"
                                           placeholder="e.g., 1000000">
                                </div>
                                <div class="col-md-6">
                                    <label for="budgetMax" class="form-label">Maximum Budget (LKR)</label>
                                    <input type="number" class="form-control" id="budgetMax" name="budget_max"
                                           placeholder="e.g., 2000000">
                                </div>
                            </div>

                            <!-- Project Details -->
                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">Project Details</h5>
                                </div>
                                <div class="col-md-4">
                                    <label for="floorArea" class="form-label">Floor Area (sq ft)</label>
                                    <input type="number" class="form-control" id="floorArea" name="floor_area"
                                           placeholder="e.g., 2000">
                                </div>
                                <div class="col-md-4">
                                    <label for="bedrooms" class="form-label">Bedrooms</label>
                                    <input type="number" class="form-control" id="bedrooms" name="bedrooms"
                                           placeholder="e.g., 3">
                                </div>
                                <div class="col-md-4">
                                    <label for="bathrooms" class="form-label">Bathrooms</label>
                                    <input type="number" class="form-control" id="bathrooms" name="bathrooms"
                                           placeholder="e.g., 2">
                                </div>
                                <div class="col-md-6">
                                    <label for="projectStartDate" class="form-label">Preferred Start Date</label>
                                    <input type="date" class="form-control" id="projectStartDate" name="project_start_date">
                                </div>
                                <div class="col-md-6">
                                    <label for="urgencyLevel" class="form-label">Urgency Level</label>
                                    <select class="form-select" id="urgencyLevel" name="urgency_level">
                                        <option value="normal">Normal</option>
                                        <option value="urgent">Urgent</option>
                                        <option value="very_urgent">Very Urgent</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Additional Requirements -->
                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">Additional Information</h5>
                                </div>
                                <div class="col-12">
                                    <label for="additionalRequirements" class="form-label">Additional Requirements</label>
                                    <textarea class="form-control" id="additionalRequirements" name="additional_requirements" rows="3"
                                              placeholder="Any specific requirements, materials, or preferences..."></textarea>
                                </div>
                                <div class="col-md-6">
                                    <label for="preferredContact" class="form-label">Preferred Contact Method</label>
                                    <select class="form-select" id="preferredContact" name="preferred_contact_method">
                                        <option value="email">Email</option>
                                        <option value="phone">Phone</option>
                                        <option value="whatsapp">WhatsApp</option>
                                    </select>
                                </div>
                            </div>

                            <!-- File Attachments -->
                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">Attachments (Optional)</h5>
                                </div>
                                <div class="col-12">
                                    <label for="attachments" class="form-label">Upload Plans, Photos, or Documents</label>
                                    <input type="file" class="form-control" id="attachments" name="attachments" multiple
                                           accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                                    <div class="form-text">Supported formats: PDF, JPG, PNG, DOC, DOCX. Max 5MB per file.</div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary py-3 px-5">
                                        <i class="fa fa-paper-plane me-2"></i>Submit Quote Request
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Cost Estimator -->
                    <div class="bg-primary rounded p-4 mb-4">
                        <h4 class="text-white mb-4">
                            <i class="fa fa-calculator me-2"></i>Quick Cost Estimate
                        </h4>
                        <p class="text-white mb-3">Get an instant cost estimate for your project</p>
                        <a href="cost-estimator.html" class="btn btn-light w-100">
                            <i class="fa fa-calculator me-2"></i>Calculate Estimate
                        </a>
                    </div>

                    <!-- How It Works -->
                    <div class="bg-light rounded p-4 mb-4">
                        <h4 class="text-primary mb-4">How It Works</h4>
                        <div class="d-flex align-items-start mb-3">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <span class="text-white fw-bold">1</span>
                            </div>
                            <div>
                                <h6 class="mb-1">Submit Request</h6>
                                <p class="mb-0 small">Fill out the form with your project details</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-start mb-3">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <span class="text-white fw-bold">2</span>
                            </div>
                            <div>
                                <h6 class="mb-1">Receive Quotes</h6>
                                <p class="mb-0 small">Get quotes from verified contractors</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-start mb-3">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <span class="text-white fw-bold">3</span>
                            </div>
                            <div>
                                <h6 class="mb-1">Compare & Choose</h6>
                                <p class="mb-0 small">Compare quotes and select the best contractor</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-start">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <span class="text-white fw-bold">4</span>
                            </div>
                            <div>
                                <h6 class="mb-1">Start Project</h6>
                                <p class="mb-0 small">Begin your construction project</p>
                            </div>
                        </div>
                    </div>

                    <!-- Tips -->
                    <div class="bg-light rounded p-4">
                        <h4 class="text-primary mb-4">
                            <i class="fa fa-lightbulb me-2"></i>Tips for Better Quotes
                        </h4>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fa fa-check text-success me-2"></i>
                                Provide detailed project description
                            </li>
                            <li class="mb-2">
                                <i class="fa fa-check text-success me-2"></i>
                                Include accurate measurements
                            </li>
                            <li class="mb-2">
                                <i class="fa fa-check text-success me-2"></i>
                                Upload relevant photos or plans
                            </li>
                            <li class="mb-2">
                                <i class="fa fa-check text-success me-2"></i>
                                Set realistic budget range
                            </li>
                            <li class="mb-0">
                                <i class="fa fa-check text-success me-2"></i>
                                Specify timeline requirements
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Quote Request Form End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-body footer mt-5 pt-5 px-0 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Contact Us</h3>
                    <p class="mb-2"><i class="fa fa-map-marker-alt text-primary me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt text-primary me-3"></i>+94 77 123 4567</p>
                    <p class="mb-2"><i class="fa fa-envelope text-primary me-3"></i><EMAIL></p>
                    <div class="d-flex pt-2">
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-twitter"></i></a>
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-facebook-f"></i></a>
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-youtube"></i></a>
                        <a class="btn btn-square btn-outline-body me-0" href=""><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Quick Links</h3>
                    <a class="btn btn-link" href="about.html">About Us</a>
                    <a class="btn btn-link" href="contact.html">Contact Us</a>
                    <a class="btn btn-link" href="service.html">Our Services</a>
                    <a class="btn btn-link" href="">Terms & Condition</a>
                    <a class="btn btn-link" href="">Support</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">For Contractors</h3>
                    <a class="btn btn-link" href="register.html">Join as Contractor</a>
                    <a class="btn btn-link" href="">Contractor Dashboard</a>
                    <a class="btn btn-link" href="">Success Stories</a>
                    <a class="btn btn-link" href="">Contractor Resources</a>
                    <a class="btn btn-link" href="">FAQ</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Newsletter</h3>
                    <p>Stay updated with the latest construction news and opportunities.</p>
                    <div class="position-relative mx-auto" style="max-width: 400px;">
                        <input class="form-control bg-transparent w-100 py-3 ps-4 pe-5" type="text" placeholder="Your email">
                        <button type="button" class="btn btn-primary py-2 position-absolute top-0 end-0 mt-2 me-2">SignUp</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid copyright">
            <div class="container">
                <div class="row">
                    <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                        &copy; <a href="#">Brick & Click</a>, All Right Reserved.
                    </div>
                    <div class="col-md-6 text-center text-md-end">
                        Designed By <a href="https://htmlcodex.com">HTML Codex</a>
                        <br> Distributed By: <a class="border-bottom" href="https://themewagon.com" target="_blank">ThemeWagon</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>

    <!-- Template Javascript -->
    <script src="js/main.js"></script>

    <!-- Quote Request Javascript -->
    <script>
        // API Configuration
        const API_BASE_URL = 'api';
        let currentUser = null;

        // API call helper
        async function apiCall(endpoint, method = 'GET', data = null) {
            const config = {
                method: method,
                headers: {},
                credentials: 'include'
            };

            if (data instanceof FormData) {
                config.body = data;
            } else if (data) {
                config.headers['Content-Type'] = 'application/json';
                config.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(`${API_BASE_URL}/${endpoint}`, config);
                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || 'An error occurred');
                }

                return result;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }

        // Check authentication
        function checkAuth() {
            const user = localStorage.getItem('user');
            const loginTime = localStorage.getItem('loginTime');

            if (!user || !loginTime) {
                return false;
            }

            const userData = JSON.parse(user);
            const timeDiff = Date.now() - parseInt(loginTime);
            const oneHour = 60 * 60 * 1000;

            if (timeDiff > oneHour) {
                localStorage.removeItem('user');
                localStorage.removeItem('loginTime');
                return false;
            }

            currentUser = userData;
            return true;
        }

        // Show message
        function showMessage(message, type = 'danger') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            alertContainer.appendChild(alertDiv);

            // Auto-hide after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 5000);
            }
        }

        // Show loading state
        function showLoading(show = true) {
            const submitBtn = document.querySelector('button[type="submit"]');

            if (show) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Submitting...';
            } else {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fa fa-paper-plane me-2"></i>Submit Quote Request';
            }
        }

        // Load service categories
        async function loadServiceCategories() {
            try {
                const response = await apiCall('service-categories');
                if (response.success) {
                    const select = document.getElementById('serviceCategory');
                    response.data.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading service categories:', error);
            }
        }

        // Load districts
        async function loadDistricts() {
            try {
                const response = await apiCall('districts');
                if (response.success) {
                    const select = document.getElementById('district');
                    response.data.forEach(district => {
                        const option = document.createElement('option');
                        option.value = district.id;
                        option.textContent = district.name;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading districts:', error);
            }
        }

        // Handle form submission
        document.getElementById('quoteRequestForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            // Check if user is logged in
            if (!checkAuth() || currentUser.user_type !== 'customer') {
                showMessage('Please login as a customer to submit quote requests. <a href="login.html" class="alert-link">Login here</a>', 'warning');
                return;
            }

            showLoading(true);

            try {
                // Collect form data
                const formData = new FormData(this);
                const data = Object.fromEntries(formData.entries());

                // Handle file uploads first if any
                const attachments = [];
                const fileInput = document.getElementById('attachments');
                if (fileInput.files.length > 0) {
                    for (let file of fileInput.files) {
                        const uploadFormData = new FormData();
                        uploadFormData.append('file', file);
                        uploadFormData.append('type', 'project');

                        const uploadResponse = await apiCall('upload', 'POST', uploadFormData);
                        if (uploadResponse.success) {
                            attachments.push(uploadResponse.data.files[0]);
                        }
                    }
                }

                // Add attachments to data
                if (attachments.length > 0) {
                    data.attachments = attachments;
                }

                // Convert numeric fields
                ['service_category_id', 'district_id', 'budget_min', 'budget_max', 'floor_area', 'bedrooms', 'bathrooms'].forEach(field => {
                    if (data[field]) {
                        data[field] = parseInt(data[field]);
                    }
                });

                // Submit quote request
                const response = await apiCall('quotes/request', 'POST', data);

                if (response.success) {
                    showMessage('Quote request submitted successfully! You will receive quotes from contractors soon.', 'success');

                    // Reset form
                    this.reset();

                    // Redirect to customer dashboard after 3 seconds
                    setTimeout(() => {
                        window.location.href = 'customer-dashboard.html';
                    }, 3000);
                } else {
                    showMessage(response.error || 'Failed to submit quote request');
                }

            } catch (error) {
                showMessage(error.message || 'Failed to submit quote request. Please try again.');
            } finally {
                showLoading(false);
            }
        });

        // File validation
        document.getElementById('attachments').addEventListener('change', function() {
            const files = this.files;
            const maxSize = 5 * 1024 * 1024; // 5MB
            const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

            for (let file of files) {
                if (file.size > maxSize) {
                    showMessage(`File "${file.name}" is too large. Maximum size is 5MB.`);
                    this.value = '';
                    return;
                }

                if (!allowedTypes.includes(file.type)) {
                    showMessage(`File "${file.name}" has an unsupported format.`);
                    this.value = '';
                    return;
                }
            }
        });

        // Set minimum date to today
        document.getElementById('projectStartDate').min = new Date().toISOString().split('T')[0];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and update navbar
            if (checkAuth()) {
                // Update navbar for logged-in user
                const loginBtn = document.querySelector('.btn-primary[href="login.html"]');
                if (loginBtn) {
                    loginBtn.textContent = `Hi, ${currentUser.first_name}`;
                    loginBtn.href = currentUser.user_type === 'customer' ? 'customer-dashboard.html' :
                                   currentUser.user_type === 'contractor' ? 'contractor-dashboard.html' :
                                   'admin-dashboard.html';
                }
            }

            // Load dropdown data
            loadServiceCategories();
            loadDistricts();
        });
    </script>
</body>

</html>
