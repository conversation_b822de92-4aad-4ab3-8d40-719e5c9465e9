<?php
/**
 * Debug API Issues
 * This script helps identify what's causing the HTML response instead of JSON
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Brick & Click API Debug</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style>";

// Test 1: Check if API files exist
echo "<h2>1. File Existence Check</h2>";
$files = [
    'api/index.php',
    'api/config/config.php',
    'api/classes/Auth.php',
    'api/classes/Database.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<span class='success'>✓ $file exists</span><br>";
    } else {
        echo "<span class='error'>✗ $file missing</span><br>";
    }
}

// Test 2: Check database connection
echo "<h2>2. Database Connection Test</h2>";
try {
    $dsn = "mysql:host=localhost;dbname=brick_click_db;charset=utf8mb4";
    $pdo = new PDO($dsn, 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "<span class='success'>✓ Database connection successful</span><br>";

    // Check if database has tables
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<span class='info'>Tables found: " . count($tables) . "</span><br>";
    if (count($tables) > 0) {
        echo "<pre>" . implode(', ', $tables) . "</pre>";
    } else {
        echo "<span class='error'>⚠ No tables found. You may need to import the database schema.</span><br>";
    }

} catch (PDOException $e) {
    echo "<span class='error'>✗ Database connection failed: " . $e->getMessage() . "</span><br>";
    echo "<span class='info'>Make sure XAMPP MySQL is running and the database 'brick_click_db' exists.</span><br>";
}

// Test 3: Test API endpoint directly
echo "<h2>3. Direct API Test</h2>";
echo "<p>Testing the API endpoint directly...</p>";

// Simulate API call
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/brick-click/api/districts';
$_SERVER['HTTP_ORIGIN'] = 'http://localhost';

// Capture output
ob_start();
try {
    // Include the API
    define('API_ACCESS', true);
    include 'api/index.php';
} catch (Exception $e) {
    echo "API Error: " . $e->getMessage();
}
$apiOutput = ob_get_clean();

echo "<h3>API Response:</h3>";
echo "<pre>" . htmlspecialchars($apiOutput) . "</pre>";

// Test 4: Check if it's valid JSON
echo "<h2>4. JSON Validation</h2>";
$jsonData = json_decode($apiOutput, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "<span class='success'>✓ API returned valid JSON</span><br>";
    echo "<pre>" . print_r($jsonData, true) . "</pre>";
} else {
    echo "<span class='error'>✗ API returned invalid JSON</span><br>";
    echo "<span class='error'>JSON Error: " . json_last_error_msg() . "</span><br>";

    // Check if it's HTML
    if (strpos($apiOutput, '<!DOCTYPE') !== false || strpos($apiOutput, '<html') !== false) {
        echo "<span class='error'>⚠ API is returning HTML instead of JSON</span><br>";
        echo "<span class='info'>This usually means there's a PHP error or the API routing is not working.</span><br>";
    }
}

// Test 5: Check PHP errors
echo "<h2>5. PHP Error Check</h2>";
if (function_exists('error_get_last')) {
    $lastError = error_get_last();
    if ($lastError) {
        echo "<span class='error'>Last PHP Error:</span><br>";
        echo "<pre>" . print_r($lastError, true) . "</pre>";
    } else {
        echo "<span class='success'>✓ No PHP errors detected</span><br>";
    }
}

// Test 6: Check API Classes
echo "<h2>6. API Classes Check</h2>";
$apiClasses = [
    'api/classes/Auth.php',
    'api/classes/Database.php'
];

foreach ($apiClasses as $class) {
    if (file_exists($class)) {
        echo "<span class='success'>✓ $class exists</span><br>";
        // Check for syntax errors
        $output = shell_exec("php -l $class 2>&1");
        if (strpos($output, 'No syntax errors') !== false) {
            echo "<span class='success'>  ✓ No syntax errors</span><br>";
        } else {
            echo "<span class='error'>  ✗ Syntax error: $output</span><br>";
        }
    } else {
        echo "<span class='error'>✗ $class missing</span><br>";
    }
}

echo "<h2>7. Recommendations</h2>";
echo "<ul>";
echo "<li>If database connection failed: Start XAMPP MySQL and create the database</li>";
echo "<li>If no tables found: Import the database_schema.sql file</li>";
echo "<li>If API returns HTML: Check PHP error logs in XAMPP</li>";
echo "<li>If JSON is invalid: There might be PHP syntax errors in the API files</li>";
echo "</ul>";

echo "<h2>8. Next Steps</h2>";
echo "<p>Based on the results above:</p>";
echo "<ol>";
echo "<li>If database connection failed: Run <a href='setup-database.php'>setup-database.php</a></li>";
echo "<li>If API returns HTML: Check for PHP syntax errors in the files above</li>";
echo "<li>If classes are missing: Make sure all API files are uploaded</li>";
echo "<li>Test the registration form at <a href='simple-register.html'>simple-register.html</a></li>";
echo "</ol>";

echo "<h2>9. Manual API Test</h2>";
echo "<p>You can also test the API manually:</p>";
echo "<pre>";
echo "// Test districts endpoint
fetch('api/districts')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error(error));

// Test registration endpoint
fetch('api/auth/register', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({
    user_type: 'customer',
    first_name: 'Test',
    last_name: 'User',
    email: '<EMAIL>',
    phone: '0771234567',
    district: 'colombo',
    password: 'password123'
  })
})
.then(response => response.text())
.then(data => console.log(data));
";
echo "</pre>";
echo "<p>Copy and paste this into your browser console (F12) to test the API directly.</p>";
?>
