<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Admin Login Test</h1>
        <p>This is a test page to debug the admin login functionality.</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="email">Admin Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="admin123" required>
            </div>
            
            <button type="submit">Test Admin Login</button>
        </form>
        
        <div id="result"></div>
        
        <hr style="margin: 30px 0;">
        
        <h3>Test Regular API Connection</h3>
        <button onclick="testApiConnection()">Test API Connection</button>
        
        <h3>Test Database Connection</h3>
        <button onclick="testDatabase()">Test Database</button>
    </div>

    <script>
        const API_BASE_URL = 'api/';

        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            showResult('Testing admin login...', 'info');
            
            try {
                const response = await fetch(API_BASE_URL + 'auth/admin-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });
                
                const result = await response.json();
                
                showResult(`Response Status: ${response.status}\n\nResponse Data:\n${JSON.stringify(result, null, 2)}`, 
                          response.ok ? 'success' : 'error');
                
            } catch (error) {
                showResult(`Network Error: ${error.message}`, 'error');
            }
        });
        
        async function testApiConnection() {
            showResult('Testing API connection...', 'info');
            
            try {
                const response = await fetch(API_BASE_URL + 'districts', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const result = await response.json();
                
                showResult(`API Connection Test:\nStatus: ${response.status}\n\nResponse:\n${JSON.stringify(result, null, 2)}`, 
                          response.ok ? 'success' : 'error');
                
            } catch (error) {
                showResult(`API Connection Error: ${error.message}`, 'error');
            }
        }
        
        async function testDatabase() {
            showResult('Testing database connection via regular login...', 'info');
            
            try {
                const response = await fetch(API_BASE_URL + 'auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'wrongpassword'
                    })
                });
                
                const result = await response.json();
                
                showResult(`Database Test (Expected to fail):\nStatus: ${response.status}\n\nResponse:\n${JSON.stringify(result, null, 2)}`, 
                          'info');
                
            } catch (error) {
                showResult(`Database Test Error: ${error.message}`, 'error');
            }
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }
    </script>
</body>
</html>
