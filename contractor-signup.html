<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Contractor Sign Up - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">
    
    <style>
        .signup-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #2C3E50 0%, #34495E 100%);
            display: flex;
            align-items: center;
            padding: 40px 0;
        }
        
        .signup-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 700px;
            width: 100%;
        }
        
        .signup-header {
            background: linear-gradient(135deg, #2C3E50 0%, #34495E 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .signup-body {
            padding: 40px;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #2C3E50 0%, #34495E 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(44, 62, 80, 0.3);
        }
        
        .form-control:focus {
            border-color: #2C3E50;
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .back-link {
            color: #2C3E50;
            text-decoration: none;
            font-weight: 500;
        }
        
        .back-link:hover {
            color: #34495E;
        }
        
        .service-checkbox {
            margin-bottom: 10px;
        }
        
        .service-checkbox .form-check-input:checked {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
    </style>
</head>

<body>
    <div class="signup-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-10">
                    <div class="signup-card">
                        <div class="signup-header">
                            <h1 class="mb-0"><i class="fa fa-hard-hat me-3"></i>Contractor Sign Up</h1>
                            <p class="mb-0 mt-2">Join Brick & Click as a verified contractor</p>
                        </div>
                        
                        <div class="signup-body">
                            <!-- Alert Messages -->
                            <div id="messageArea"></div>
                            
                            <form id="contractorSignupForm">
                                <!-- Personal Information -->
                                <h5 class="text-primary mb-3"><i class="fa fa-user me-2"></i>Personal Information</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="firstName" placeholder="First Name" required>
                                            <label for="firstName">First Name</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="lastName" placeholder="Last Name" required>
                                            <label for="lastName">Last Name</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="email" class="form-control" id="email" placeholder="Email" required>
                                            <label for="email">Email Address</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="tel" class="form-control" id="phone" placeholder="Phone" required>
                                            <label for="phone">Phone Number</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Business Information -->
                                <h5 class="text-primary mb-3 mt-4"><i class="fa fa-building me-2"></i>Business Information</h5>
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="businessName" placeholder="Business Name" required>
                                    <label for="businessName">Business Name</label>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="number" class="form-control" id="yearsExperience" placeholder="Years" min="1" required>
                                            <label for="yearsExperience">Years of Experience</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="cidaGrade" required>
                                                <option value="">Select CIDA Grade</option>
                                                <option value="C1">C1 - Major Construction</option>
                                                <option value="C2">C2 - Medium Construction</option>
                                                <option value="C3">C3 - Small Construction</option>
                                                <option value="C4">C4 - Minor Construction</option>
                                                <option value="C5">C5 - Micro Construction</option>
                                            </select>
                                            <label for="cidaGrade">CIDA Grade</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating">
                                    <input type="text" class="form-control" id="cidaNumber" placeholder="CIDA Number" required>
                                    <label for="cidaNumber">CIDA Registration Number</label>
                                </div>

                                <!-- Service Types -->
                                <h5 class="text-primary mb-3 mt-4"><i class="fa fa-tools me-2"></i>Services Offered</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="service-checkbox">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="residential_construction" id="residential">
                                                <label class="form-check-label" for="residential">Residential Construction</label>
                                            </div>
                                        </div>
                                        <div class="service-checkbox">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="commercial_construction" id="commercial">
                                                <label class="form-check-label" for="commercial">Commercial Construction</label>
                                            </div>
                                        </div>
                                        <div class="service-checkbox">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="renovation" id="renovation">
                                                <label class="form-check-label" for="renovation">Renovation & Remodeling</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="service-checkbox">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="interior_design" id="interior">
                                                <label class="form-check-label" for="interior">Interior Design</label>
                                            </div>
                                        </div>
                                        <div class="service-checkbox">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="landscaping" id="landscaping">
                                                <label class="form-check-label" for="landscaping">Landscaping</label>
                                            </div>
                                        </div>
                                        <div class="service-checkbox">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="electrical" id="electrical">
                                                <label class="form-check-label" for="electrical">Electrical Work</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Service Areas -->
                                <div class="form-floating">
                                    <select class="form-select" id="serviceAreas" multiple required>
                                        <option value="colombo">Colombo</option>
                                        <option value="gampaha">Gampaha</option>
                                        <option value="kalutara">Kalutara</option>
                                        <option value="kandy">Kandy</option>
                                        <option value="matale">Matale</option>
                                        <option value="galle">Galle</option>
                                        <option value="kurunegala">Kurunegala</option>
                                        <option value="anuradhapura">Anuradhapura</option>
                                        <option value="polonnaruwa">Polonnaruwa</option>
                                        <option value="badulla">Badulla</option>
                                        <option value="ratnapura">Ratnapura</option>
                                        <option value="kegalle">Kegalle</option>
                                        <option value="nuwara_eliya">Nuwara Eliya</option>
                                        <option value="hambantota">Hambantota</option>
                                        <option value="matara">Matara</option>
                                        <option value="jaffna">Jaffna</option>
                                        <option value="kilinochchi">Kilinochchi</option>
                                        <option value="mannar">Mannar</option>
                                        <option value="vavuniya">Vavuniya</option>
                                        <option value="mullaitivu">Mullaitivu</option>
                                        <option value="batticaloa">Batticaloa</option>
                                        <option value="ampara">Ampara</option>
                                        <option value="trincomalee">Trincomalee</option>
                                        <option value="puttalam">Puttalam</option>
                                        <option value="moneragala">Moneragala</option>
                                    </select>
                                    <label for="serviceAreas">Service Areas (Hold Ctrl to select multiple)</label>
                                </div>

                                <!-- Account Security -->
                                <h5 class="text-primary mb-3 mt-4"><i class="fa fa-lock me-2"></i>Account Security</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="password" class="form-control" id="password" placeholder="Password" required>
                                            <label for="password">Password</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="password" class="form-control" id="confirmPassword" placeholder="Confirm Password" required>
                                            <label for="confirmPassword">Confirm Password</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check mb-4">
                                    <input class="form-check-input" type="checkbox" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        I agree to the <a href="#" class="text-primary">Terms and Conditions</a> and <a href="#" class="text-primary">Privacy Policy</a>
                                    </label>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fa fa-info-circle me-2"></i>
                                    <strong>Note:</strong> Your account will be reviewed and verified by our admin team before activation. You will receive an email notification once approved.
                                </div>

                                <button type="submit" class="btn btn-primary w-100 py-3 mb-3">
                                    <i class="fa fa-hard-hat me-2"></i>Submit for Verification
                                </button>
                            </form>
                            
                            <div class="text-center">
                                <p class="mb-2">Already have an account? <a href="login.html" class="text-primary">Login here</a></p>
                                <p class="mb-0">Looking for contractors? <a href="customer-signup.html" class="text-primary">Sign up as customer</a></p>
                                <hr class="my-3">
                                <a href="index.html" class="back-link"><i class="fa fa-arrow-left me-2"></i>Back to Home</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const API_BASE_URL = 'api';

        function showMessage(message, type = 'danger') {
            const messageArea = document.getElementById('messageArea');
            messageArea.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }

        function clearMessages() {
            document.getElementById('messageArea').innerHTML = '';
        }

        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const config = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                };

                if (data) {
                    config.body = JSON.stringify(data);
                }

                const response = await fetch(`${API_BASE_URL}/${endpoint}`, config);
                
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const textResponse = await response.text();
                    throw new Error('Server configuration error. Please make sure the database is set up correctly.');
                }
                
                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || result.message || 'An error occurred');
                }

                return result;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }

        document.getElementById('contractorSignupForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            clearMessages();

            const firstName = document.getElementById('firstName').value.trim();
            const lastName = document.getElementById('lastName').value.trim();
            const email = document.getElementById('email').value.trim();
            const phone = document.getElementById('phone').value.trim();
            const businessName = document.getElementById('businessName').value.trim();
            const yearsExperience = parseInt(document.getElementById('yearsExperience').value);
            const cidaGrade = document.getElementById('cidaGrade').value;
            const cidaNumber = document.getElementById('cidaNumber').value.trim();
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            // Get selected services
            const serviceTypes = [];
            document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                if (checkbox.value && checkbox.value !== 'on') {
                    serviceTypes.push(checkbox.value);
                }
            });

            // Get selected service areas
            const serviceAreas = Array.from(document.getElementById('serviceAreas').selectedOptions).map(option => option.value);

            // Validation
            if (!firstName || !lastName || !email || !phone || !businessName || !yearsExperience || !cidaGrade || !cidaNumber || !password || !confirmPassword) {
                showMessage('Please fill in all required fields');
                return;
            }

            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage('Please enter a valid email address');
                return;
            }

            if (password !== confirmPassword) {
                showMessage('Passwords do not match');
                return;
            }

            if (password.length < 6) {
                showMessage('Password must be at least 6 characters long');
                return;
            }

            if (serviceTypes.length === 0) {
                showMessage('Please select at least one service type');
                return;
            }

            if (serviceAreas.length === 0) {
                showMessage('Please select at least one service area');
                return;
            }

            if (yearsExperience < 1) {
                showMessage('Years of experience must be at least 1');
                return;
            }

            const submitBtn = document.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Submitting for Verification...';

            try {
                const formData = {
                    user_type: 'contractor',
                    first_name: firstName,
                    last_name: lastName,
                    email: email,
                    phone: phone,
                    business_name: businessName,
                    years_experience: yearsExperience,
                    cida_registration_number: cidaNumber,
                    cida_grade: cidaGrade,
                    password: password,
                    services: serviceTypes,
                    service_areas: serviceAreas,
                    preferred_language: 'en'
                };

                const response = await apiCall('auth/register', 'POST', formData);

                if (response.success) {
                    showMessage('Registration submitted successfully! Your account will be reviewed and verified by our admin team. Redirecting to contractor dashboard...', 'success');

                    // Store user info for dashboard
                    localStorage.setItem('userType', 'contractor');
                    localStorage.setItem('userName', firstName + ' ' + lastName);
                    localStorage.setItem('userEmail', email);
                    localStorage.setItem('businessName', businessName);

                    // Redirect to contractor dashboard after 3 seconds
                    setTimeout(() => {
                        window.location.href = 'contractor-dashboard.html';
                    }, 3000);
                } else {
                    showMessage(response.error || 'Registration failed');
                }
            } catch (error) {
                showMessage(error.message || 'Registration failed. Please try again.');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fa fa-hard-hat me-2"></i>Submit for Verification';
            }
        });
    </script>
</body>
</html>
