<?php
/**
 * Admin Controller
 * Handles admin operations including contractor verification and content moderation
 */

class AdminController {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get pending contractor verifications
     */
    public function getPendingVerifications($page = 1, $limit = 20) {
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT cp.*, CONCAT(u.first_name, ' ', u.last_name) as contractor_name,
                u.email, u.phone, u.created_at as registration_date,
                GROUP_CONCAT(DISTINCT sc.name) as services,
                GROUP_CONCAT(DISTINCT d.name) as service_areas
                FROM contractor_profiles cp
                JOIN users u ON cp.user_id = u.id
                LEFT JOIN contractor_services cs ON cp.id = cs.contractor_id
                LEFT JOIN service_categories sc ON cs.service_category_id = sc.id
                LEFT JOIN contractor_service_areas csa ON cp.id = csa.contractor_id
                LEFT JOIN districts d ON csa.district_id = d.id
                WHERE cp.verification_status = 'pending'
                GROUP BY cp.id
                ORDER BY cp.created_at ASC
                LIMIT :limit OFFSET :offset";
        
        $verifications = $this->db->fetchAll($sql, [
            ':limit' => $limit,
            ':offset' => $offset
        ]);
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM contractor_profiles WHERE verification_status = 'pending'";
        $totalResult = $this->db->fetchOne($countSql);
        $total = $totalResult['total'];
        
        return [
            'verifications' => $verifications,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_items' => $total,
                'items_per_page' => $limit
            ]
        ];
    }
    
    /**
     * Get contractor verification details
     */
    public function getVerificationDetails($contractorId) {
        $sql = "SELECT cp.*, CONCAT(u.first_name, ' ', u.last_name) as contractor_name,
                u.email, u.phone, u.created_at as registration_date
                FROM contractor_profiles cp
                JOIN users u ON cp.user_id = u.id
                WHERE cp.id = :contractor_id";
        
        $contractor = $this->db->fetchOne($sql, [':contractor_id' => $contractorId]);
        
        if (!$contractor) {
            throw new Exception("Contractor not found");
        }
        
        // Get services
        $services = $this->db->fetchAll(
            "SELECT sc.name FROM contractor_services cs 
             JOIN service_categories sc ON cs.service_category_id = sc.id 
             WHERE cs.contractor_id = :contractor_id",
            [':contractor_id' => $contractorId]
        );
        
        // Get service areas
        $serviceAreas = $this->db->fetchAll(
            "SELECT d.name FROM contractor_service_areas csa 
             JOIN districts d ON csa.district_id = d.id 
             WHERE csa.contractor_id = :contractor_id",
            [':contractor_id' => $contractorId]
        );
        
        $contractor['services'] = array_column($services, 'name');
        $contractor['service_areas'] = array_column($serviceAreas, 'name');
        
        return $contractor;
    }
    
    /**
     * Approve contractor verification
     */
    public function approveContractor($contractorId, $adminId, $notes = null) {
        $contractor = $this->db->fetchOne(
            "SELECT cp.*, u.id as user_id FROM contractor_profiles cp 
             JOIN users u ON cp.user_id = u.id 
             WHERE cp.id = :contractor_id",
            [':contractor_id' => $contractorId]
        );
        
        if (!$contractor) {
            throw new Exception("Contractor not found");
        }
        
        if ($contractor['verification_status'] !== 'pending') {
            throw new Exception("Contractor verification is not pending");
        }
        
        try {
            $this->db->beginTransaction();
            
            // Update verification status
            $this->db->update(
                "UPDATE contractor_profiles SET 
                 verification_status = 'approved', 
                 verified_at = NOW(), 
                 verified_by = :admin_id,
                 verification_notes = :notes
                 WHERE id = :contractor_id",
                [
                    ':admin_id' => $adminId,
                    ':notes' => $notes,
                    ':contractor_id' => $contractorId
                ]
            );
            
            // Update user status to active
            $this->db->update(
                "UPDATE users SET status = 'active' WHERE id = :user_id",
                [':user_id' => $contractor['user_id']]
            );
            
            $this->db->commit();
            
            // Send notification to contractor
            NotificationHelper::verificationStatus(
                $contractor['user_id'],
                'approved'
            );
            
            // Log activity
            log_activity($adminId, 'contractor_approved', 'contractor_profile', $contractorId, 'Contractor verification approved');
            
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Reject contractor verification
     */
    public function rejectContractor($contractorId, $adminId, $reason) {
        if (empty($reason)) {
            throw new Exception("Rejection reason is required");
        }
        
        $contractor = $this->db->fetchOne(
            "SELECT cp.*, u.id as user_id FROM contractor_profiles cp 
             JOIN users u ON cp.user_id = u.id 
             WHERE cp.id = :contractor_id",
            [':contractor_id' => $contractorId]
        );
        
        if (!$contractor) {
            throw new Exception("Contractor not found");
        }
        
        if ($contractor['verification_status'] !== 'pending') {
            throw new Exception("Contractor verification is not pending");
        }
        
        try {
            $this->db->beginTransaction();
            
            // Update verification status
            $this->db->update(
                "UPDATE contractor_profiles SET 
                 verification_status = 'rejected', 
                 verified_at = NOW(), 
                 verified_by = :admin_id,
                 verification_notes = :reason
                 WHERE id = :contractor_id",
                [
                    ':admin_id' => $adminId,
                    ':reason' => $reason,
                    ':contractor_id' => $contractorId
                ]
            );
            
            $this->db->commit();
            
            // Send notification to contractor
            NotificationHelper::verificationStatus(
                $contractor['user_id'],
                'rejected',
                $reason
            );
            
            // Log activity
            log_activity($adminId, 'contractor_rejected', 'contractor_profile', $contractorId, 'Contractor verification rejected');
            
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Get system statistics
     */
    public function getSystemStatistics() {
        $stats = [];
        
        // User statistics
        $userStats = $this->db->fetchOne(
            "SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN user_type = 'customer' THEN 1 END) as total_customers,
                COUNT(CASE WHEN user_type = 'contractor' THEN 1 END) as total_contractors,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_30_days
             FROM users"
        );
        
        // Contractor verification statistics
        $contractorStats = $this->db->fetchOne(
            "SELECT 
                COUNT(*) as total_contractor_profiles,
                COUNT(CASE WHEN verification_status = 'pending' THEN 1 END) as pending_verifications,
                COUNT(CASE WHEN verification_status = 'approved' THEN 1 END) as verified_contractors,
                COUNT(CASE WHEN verification_status = 'rejected' THEN 1 END) as rejected_contractors
             FROM contractor_profiles"
        );
        
        // Project statistics
        $projectStats = $this->db->fetchOne(
            "SELECT 
                COUNT(*) as total_projects,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_projects,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_projects,
                SUM(total_amount) as total_project_value
             FROM projects"
        );
        
        // Quote statistics
        $quoteStats = $this->db->fetchOne(
            "SELECT 
                COUNT(*) as total_quote_requests,
                COUNT(CASE WHEN status = 'open' THEN 1 END) as open_quotes,
                COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed_quotes
             FROM quote_requests"
        );
        
        // Review statistics
        $reviewStats = $this->db->fetchOne(
            "SELECT 
                COUNT(*) as total_reviews,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_reviews,
                COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_reviews,
                AVG(rating) as average_rating
             FROM reviews"
        );
        
        return [
            'users' => $userStats,
            'contractors' => $contractorStats,
            'projects' => $projectStats,
            'quotes' => $quoteStats,
            'reviews' => $reviewStats
        ];
    }
    
    /**
     * Get recent system activities
     */
    public function getRecentActivities($limit = 50) {
        $sql = "SELECT al.*, CONCAT(u.first_name, ' ', u.last_name) as user_name
                FROM activity_logs al
                JOIN users u ON al.user_id = u.id
                ORDER BY al.created_at DESC
                LIMIT :limit";
        
        return $this->db->fetchAll($sql, [':limit' => $limit]);
    }
    
    /**
     * Get user management data
     */
    public function getUserManagement($userType = null, $status = null, $page = 1, $limit = 20) {
        $offset = ($page - 1) * $limit;
        
        $conditions = [];
        $params = [];
        
        if ($userType) {
            $conditions[] = "user_type = :user_type";
            $params[':user_type'] = $userType;
        }
        
        if ($status) {
            $conditions[] = "status = :status";
            $params[':status'] = $status;
        }
        
        $whereClause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
        
        $sql = "SELECT id, first_name, last_name, email, phone, user_type, status, 
                created_at, last_login_at
                FROM users 
                $whereClause
                ORDER BY created_at DESC
                LIMIT :limit OFFSET :offset";
        
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;
        
        $users = $this->db->fetchAll($sql, $params);
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM users $whereClause";
        $countParams = array_filter($params, function($key) {
            return !in_array($key, [':limit', ':offset']);
        }, ARRAY_FILTER_USE_KEY);
        
        $totalResult = $this->db->fetchOne($countSql, $countParams);
        $total = $totalResult['total'];
        
        return [
            'users' => $users,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_items' => $total,
                'items_per_page' => $limit
            ]
        ];
    }
    
    /**
     * Update user status
     */
    public function updateUserStatus($userId, $adminId, $status, $reason = null) {
        $validStatuses = ['active', 'suspended', 'banned'];
        if (!in_array($status, $validStatuses)) {
            throw new Exception("Invalid user status");
        }
        
        $user = $this->db->fetchOne(
            "SELECT * FROM users WHERE id = :user_id",
            [':user_id' => $userId]
        );
        
        if (!$user) {
            throw new Exception("User not found");
        }
        
        $this->db->update(
            "UPDATE users SET status = :status WHERE id = :user_id",
            [':status' => $status, ':user_id' => $userId]
        );
        
        // Log activity
        log_activity($adminId, 'user_status_updated', 'user', $userId, "User status changed to {$status}" . ($reason ? ": {$reason}" : ""));
        
        return true;
    }
    
    /**
     * Get content moderation queue
     */
    public function getContentModerationQueue($contentType = null, $page = 1, $limit = 20) {
        $offset = ($page - 1) * $limit;
        
        $items = [];
        
        // Get pending reviews
        if (!$contentType || $contentType === 'reviews') {
            $reviewController = new ReviewController();
            $pendingReviews = $reviewController->getPendingReviews($page, $limit);
            foreach ($pendingReviews as $review) {
                $review['content_type'] = 'review';
                $items[] = $review;
            }
        }
        
        // Get reported content (implement as needed)
        // Add other content types for moderation
        
        return $items;
    }
    
    /**
     * Generate system report
     */
    public function generateSystemReport($reportType, $startDate = null, $endDate = null) {
        $startDate = $startDate ?: date('Y-m-01'); // First day of current month
        $endDate = $endDate ?: date('Y-m-d'); // Today
        
        switch ($reportType) {
            case 'user_registrations':
                return $this->generateUserRegistrationReport($startDate, $endDate);
            case 'project_activity':
                return $this->generateProjectActivityReport($startDate, $endDate);
            case 'revenue':
                return $this->generateRevenueReport($startDate, $endDate);
            default:
                throw new Exception("Invalid report type");
        }
    }
    
    /**
     * Generate user registration report
     */
    private function generateUserRegistrationReport($startDate, $endDate) {
        $sql = "SELECT 
                DATE(created_at) as date,
                COUNT(*) as total_registrations,
                COUNT(CASE WHEN user_type = 'customer' THEN 1 END) as customer_registrations,
                COUNT(CASE WHEN user_type = 'contractor' THEN 1 END) as contractor_registrations
                FROM users 
                WHERE created_at BETWEEN :start_date AND :end_date
                GROUP BY DATE(created_at)
                ORDER BY date";
        
        return $this->db->fetchAll($sql, [
            ':start_date' => $startDate,
            ':end_date' => $endDate . ' 23:59:59'
        ]);
    }
    
    /**
     * Generate project activity report
     */
    private function generateProjectActivityReport($startDate, $endDate) {
        $sql = "SELECT 
                DATE(created_at) as date,
                COUNT(*) as projects_created,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as projects_completed,
                SUM(total_amount) as total_value
                FROM projects 
                WHERE created_at BETWEEN :start_date AND :end_date
                GROUP BY DATE(created_at)
                ORDER BY date";
        
        return $this->db->fetchAll($sql, [
            ':start_date' => $startDate,
            ':end_date' => $endDate . ' 23:59:59'
        ]);
    }
    
    /**
     * Generate revenue report (placeholder)
     */
    private function generateRevenueReport($startDate, $endDate) {
        // Implement based on your revenue model
        // This could include platform fees, subscription fees, etc.
        return [];
    }
}
?>
