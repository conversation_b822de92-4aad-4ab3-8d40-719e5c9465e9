<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Register - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="construction, contractors, Sri Lanka, verified contractors, CIDA" name="keywords">
    <meta content="Register with Brick & Click - Sri Lanka's leading construction contractor platform" name="description">

    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border position-relative text-primary" style="width: 6rem; height: 6rem;" role="status"></div>
        <img class="position-absolute top-50 start-50 translate-middle" src="img/icons/icon-1.png" alt="Icon">
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5 wow fadeIn" data-wow-delay="0.1s">
        <a href="index.html" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="index.html" class="nav-item nav-link">Home</a>
                <a href="about.html" class="nav-item nav-link">About</a>
                <a href="service.html" class="nav-item nav-link">Services</a>
                <a href="contact.html" class="nav-item nav-link">Contact</a>
            </div>
            <div class="d-none d-lg-block">
                <a href="login.html" class="btn btn-outline-primary py-2 px-4 me-2">Login</a>
                <a href="register.html" class="btn btn-primary py-2 px-4 active">Sign Up</a>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Page Header Start -->
    <div class="container-fluid page-header py-5 mb-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <h1 class="display-1 text-white animated slideInDown">Register</h1>
            <nav aria-label="breadcrumb animated slideInDown">
                <ol class="breadcrumb text-uppercase mb-0">
                    <li class="breadcrumb-item"><a class="text-white" href="index.html">Home</a></li>
                    <li class="breadcrumb-item text-primary active" aria-current="page">Register</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Registration Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="wow fadeInUp" data-wow-delay="0.1s">
                        <div class="text-center mb-5">
                            <h2 class="section-title text-center text-primary px-3">Join Us</h2>
                            <h1 class="mb-3">Create Your Account</h1>
                            <p class="mb-4">Choose your account type to get started</p>
                        </div>

                        <!-- Account Type Selection -->
                        <div class="row g-4 mb-5">
                            <div class="col-md-6">
                                <div class="account-type-card h-100 p-4 border rounded text-center" data-type="customer">
                                    <div class="mb-3">
                                        <i class="fa fa-user fa-3x text-primary"></i>
                                    </div>
                                    <h4>Customer</h4>
                                    <p class="mb-3">Looking for construction contractors? Find verified professionals for your project.</p>
                                    <ul class="list-unstyled text-start">
                                        <li><i class="fa fa-check text-primary me-2"></i>Search verified contractors</li>
                                        <li><i class="fa fa-check text-primary me-2"></i>Request and compare quotes</li>
                                        <li><i class="fa fa-check text-primary me-2"></i>Leave reviews and ratings</li>
                                        <li><i class="fa fa-check text-primary me-2"></i>Save favorite contractors</li>
                                    </ul>
                                    <button class="btn btn-primary w-100" onclick="selectAccountType('customer')">Register as Customer</button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="account-type-card h-100 p-4 border rounded text-center" data-type="contractor">
                                    <div class="mb-3">
                                        <i class="fa fa-hard-hat fa-3x text-primary"></i>
                                    </div>
                                    <h4>Contractor</h4>
                                    <p class="mb-3">Are you a construction professional? Join our verified contractor network.</p>
                                    <ul class="list-unstyled text-start">
                                        <li><i class="fa fa-check text-primary me-2"></i>Get verified CIDA status</li>
                                        <li><i class="fa fa-check text-primary me-2"></i>Receive quote requests</li>
                                        <li><i class="fa fa-check text-primary me-2"></i>Showcase your portfolio</li>
                                        <li><i class="fa fa-check text-primary me-2"></i>Build your reputation</li>
                                    </ul>
                                    <button class="btn btn-primary w-100" onclick="selectAccountType('contractor')">Register as Contractor</button>
                                </div>
                            </div>
                        </div>

                        <!-- Registration Forms -->
                        <div id="registrationForms" style="display: none;">
                            <!-- Customer Registration Form -->
                            <div id="customerForm" class="registration-form" style="display: none;">
                                <div class="bg-light rounded p-5">
                                    <h3 class="mb-4 text-center">Customer Registration</h3>
                                    <form id="customerRegistrationForm">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="customerFirstName" placeholder="First Name" required>
                                                    <label for="customerFirstName">First Name</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="customerLastName" placeholder="Last Name" required>
                                                    <label for="customerLastName">Last Name</label>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="form-floating">
                                                    <input type="email" class="form-control" id="customerEmail" placeholder="Email" required>
                                                    <label for="customerEmail">Email Address</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="tel" class="form-control" id="customerPhone" placeholder="Phone" required>
                                                    <label for="customerPhone">Phone Number</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <select class="form-select" id="customerDistrict" required>
                                                        <option value="">Select District</option>
                                                        <option value="colombo">Colombo</option>
                                                        <option value="gampaha">Gampaha</option>
                                                        <option value="kalutara">Kalutara</option>
                                                        <option value="kandy">Kandy</option>
                                                        <option value="matale">Matale</option>
                                                        <option value="nuwara-eliya">Nuwara Eliya</option>
                                                        <option value="galle">Galle</option>
                                                        <option value="matara">Matara</option>
                                                        <option value="hambantota">Hambantota</option>
                                                        <option value="jaffna">Jaffna</option>
                                                        <option value="kilinochchi">Kilinochchi</option>
                                                        <option value="mannar">Mannar</option>
                                                        <option value="vavuniya">Vavuniya</option>
                                                        <option value="mullaitivu">Mullaitivu</option>
                                                        <option value="batticaloa">Batticaloa</option>
                                                        <option value="ampara">Ampara</option>
                                                        <option value="trincomalee">Trincomalee</option>
                                                        <option value="kurunegala">Kurunegala</option>
                                                        <option value="puttalam">Puttalam</option>
                                                        <option value="anuradhapura">Anuradhapura</option>
                                                        <option value="polonnaruwa">Polonnaruwa</option>
                                                        <option value="badulla">Badulla</option>
                                                        <option value="moneragala">Moneragala</option>
                                                        <option value="ratnapura">Ratnapura</option>
                                                        <option value="kegalle">Kegalle</option>
                                                    </select>
                                                    <label for="customerDistrict">District</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="password" class="form-control" id="customerPassword" placeholder="Password" required>
                                                    <label for="customerPassword">Password</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="password" class="form-control" id="customerConfirmPassword" placeholder="Confirm Password" required>
                                                    <label for="customerConfirmPassword">Confirm Password</label>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="customerTerms" required>
                                                    <label class="form-check-label" for="customerTerms">
                                                        I agree to the <a href="#" class="text-primary">Terms and Conditions</a> and <a href="#" class="text-primary">Privacy Policy</a>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <button class="btn btn-primary w-100 py-3" type="submit">Create Customer Account</button>
                                            </div>
                                            <div class="col-12 text-center">
                                                <button type="button" class="btn btn-link" onclick="goBack()">← Choose Different Account Type</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Contractor Registration Form -->
                            <div id="contractorForm" class="registration-form" style="display: none;">
                                <div class="bg-light rounded p-5">
                                    <h3 class="mb-4 text-center">Contractor Registration</h3>
                                    <form id="contractorRegistrationForm">
                                        <div class="row g-3">
                                            <!-- Personal Information -->
                                            <div class="col-12">
                                                <h5 class="text-primary">Personal Information</h5>
                                                <hr>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="contractorFirstName" placeholder="First Name" required>
                                                    <label for="contractorFirstName">First Name</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="contractorLastName" placeholder="Last Name" required>
                                                    <label for="contractorLastName">Last Name</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="email" class="form-control" id="contractorEmail" placeholder="Email" required>
                                                    <label for="contractorEmail">Email Address</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="tel" class="form-control" id="contractorPhone" placeholder="Phone" required>
                                                    <label for="contractorPhone">Phone Number</label>
                                                </div>
                                            </div>

                                            <!-- Business Information -->
                                            <div class="col-12 mt-4">
                                                <h5 class="text-primary">Business Information</h5>
                                                <hr>
                                            </div>
                                            <div class="col-12">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="businessName" placeholder="Business Name" required>
                                                    <label for="businessName">Business Name</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="businessRegNumber" placeholder="Business Registration Number">
                                                    <label for="businessRegNumber">Business Registration Number</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="number" class="form-control" id="yearsExperience" placeholder="Years of Experience" min="0" required>
                                                    <label for="yearsExperience">Years of Experience</label>
                                                </div>
                                            </div>

                                            <!-- Service Information -->
                                            <div class="col-12 mt-4">
                                                <h5 class="text-primary">Service Information</h5>
                                                <hr>
                                            </div>
                                            <div class="col-12">
                                                <label class="form-label">Service Types (Select all that apply)</label>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="serviceResidential" value="residential">
                                                            <label class="form-check-label" for="serviceResidential">Residential Construction</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="serviceCommercial" value="commercial">
                                                            <label class="form-check-label" for="serviceCommercial">Commercial Construction</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="serviceRenovation" value="renovation">
                                                            <label class="form-check-label" for="serviceRenovation">Renovation & Remodeling</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="serviceElectrical" value="electrical">
                                                            <label class="form-check-label" for="serviceElectrical">Electrical Work</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="servicePlumbing" value="plumbing">
                                                            <label class="form-check-label" for="servicePlumbing">Plumbing</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="serviceInterior" value="interior">
                                                            <label class="form-check-label" for="serviceInterior">Interior Design</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-12">
                                                <label class="form-label">Service Areas (Select all districts you serve)</label>
                                                <select class="form-select" id="serviceAreas" multiple size="5" required>
                                                    <option value="colombo">Colombo</option>
                                                    <option value="gampaha">Gampaha</option>
                                                    <option value="kalutara">Kalutara</option>
                                                    <option value="kandy">Kandy</option>
                                                    <option value="matale">Matale</option>
                                                    <option value="nuwara-eliya">Nuwara Eliya</option>
                                                    <option value="galle">Galle</option>
                                                    <option value="matara">Matara</option>
                                                    <option value="hambantota">Hambantota</option>
                                                    <option value="jaffna">Jaffna</option>
                                                    <option value="kurunegala">Kurunegala</option>
                                                    <option value="anuradhapura">Anuradhapura</option>
                                                    <option value="ratnapura">Ratnapura</option>
                                                    <option value="kegalle">Kegalle</option>
                                                </select>
                                                <small class="form-text text-muted">Hold Ctrl (Cmd on Mac) to select multiple districts</small>
                                            </div>

                                            <!-- CIDA Information -->
                                            <div class="col-12 mt-4">
                                                <h5 class="text-primary">CIDA Certification</h5>
                                                <hr>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="cidaNumber" placeholder="CIDA Registration Number" required>
                                                    <label for="cidaNumber">CIDA Registration Number</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <select class="form-select" id="cidaGrade" required>
                                                        <option value="">Select CIDA Grade</option>
                                                        <option value="C1">C1</option>
                                                        <option value="C2">C2</option>
                                                        <option value="C3">C3</option>
                                                        <option value="C4">C4</option>
                                                        <option value="C5">C5</option>
                                                        <option value="C6">C6</option>
                                                        <option value="C7">C7</option>
                                                        <option value="C8">C8</option>
                                                        <option value="C9">C9</option>
                                                        <option value="C10">C10</option>
                                                    </select>
                                                    <label for="cidaGrade">CIDA Grade</label>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <label for="cidaDocument" class="form-label">CIDA Certificate (PDF, JPG, PNG - Max 5MB)</label>
                                                <input class="form-control" type="file" id="cidaDocument" accept=".pdf,.jpg,.jpeg,.png" required>
                                            </div>
                                            <div class="col-12">
                                                <label for="businessLicense" class="form-label">Business License (PDF, JPG, PNG - Max 5MB)</label>
                                                <input class="form-control" type="file" id="businessLicense" accept=".pdf,.jpg,.jpeg,.png">
                                            </div>

                                            <!-- Account Security -->
                                            <div class="col-12 mt-4">
                                                <h5 class="text-primary">Account Security</h5>
                                                <hr>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="password" class="form-control" id="contractorPassword" placeholder="Password" required>
                                                    <label for="contractorPassword">Password</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="password" class="form-control" id="contractorConfirmPassword" placeholder="Confirm Password" required>
                                                    <label for="contractorConfirmPassword">Confirm Password</label>
                                                </div>
                                            </div>

                                            <div class="col-12">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="contractorTerms" required>
                                                    <label class="form-check-label" for="contractorTerms">
                                                        I agree to the <a href="#" class="text-primary">Terms and Conditions</a> and <a href="#" class="text-primary">Privacy Policy</a>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="contractorVerification" required>
                                                    <label class="form-check-label" for="contractorVerification">
                                                        I understand that my account will be reviewed and verified by the admin team before activation
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <button class="btn btn-primary w-100 py-3" type="submit">Submit for Verification</button>
                                            </div>
                                            <div class="col-12 text-center">
                                                <button type="button" class="btn btn-link" onclick="goBack()">← Choose Different Account Type</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <p class="mb-0">Already have an account? <a href="login.html" class="text-primary fw-bold">Login here</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Registration End -->

    <!-- Language Selector Start -->
    <div class="container-xxl py-3">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center">
                    <div class="language-selector">
                        <label class="me-3">Language:</label>
                        <select class="form-select d-inline-block w-auto" id="languageSelect">
                            <option value="en" selected>English</option>
                            <option value="si">සිංහල</option>
                            <option value="ta">தமிழ்</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Language Selector End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-body footer mt-5 pt-5 px-0 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Contact Us</h3>
                    <p class="mb-2"><i class="fa fa-map-marker-alt text-primary me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt text-primary me-3"></i>+94 77 123 4567</p>
                    <p class="mb-2"><i class="fa fa-envelope text-primary me-3"></i><EMAIL></p>
                    <div class="d-flex pt-2">
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-twitter"></i></a>
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-facebook-f"></i></a>
                        <a class="btn btn-square btn-outline-body me-1" href=""><i class="fab fa-youtube"></i></a>
                        <a class="btn btn-square btn-outline-body me-0" href=""><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Quick Links</h3>
                    <a class="btn btn-link" href="about.html">About Us</a>
                    <a class="btn btn-link" href="contact.html">Contact Us</a>
                    <a class="btn btn-link" href="service.html">Our Services</a>
                    <a class="btn btn-link" href="">Terms & Condition</a>
                    <a class="btn btn-link" href="">Support</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">For Contractors</h3>
                    <a class="btn btn-link" href="register.html">Join as Contractor</a>
                    <a class="btn btn-link" href="">Contractor Dashboard</a>
                    <a class="btn btn-link" href="">Success Stories</a>
                    <a class="btn btn-link" href="">Contractor Resources</a>
                    <a class="btn btn-link" href="">FAQ</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Newsletter</h3>
                    <p>Stay updated with the latest construction news and opportunities.</p>
                    <div class="position-relative mx-auto" style="max-width: 400px;">
                        <input class="form-control bg-transparent w-100 py-3 ps-4 pe-5" type="text" placeholder="Your email">
                        <button type="button" class="btn btn-primary py-2 position-absolute top-0 end-0 mt-2 me-2">SignUp</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid copyright">
            <div class="container">
                <div class="row">
                    <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                        &copy; <a href="#">Brick & Click</a>, All Right Reserved.
                    </div>
                    <div class="col-md-6 text-center text-md-end">
                        Designed By <a href="https://htmlcodex.com">HTML Codex</a>
                        <br> Distributed By: <a class="border-bottom" href="https://themewagon.com" target="_blank">ThemeWagon</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>

    <!-- Template Javascript -->
    <script src="js/main.js"></script>

    <!-- Registration Page Javascript -->
    <script>
        // API Configuration
        const API_BASE_URL = 'api';
        let selectedAccountType = '';

        // Show loading state
        function showLoading(formId, show = true) {
            const form = document.getElementById(formId);
            const submitBtn = form.querySelector('button[type="submit"]');

            if (show) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Processing...';
            } else {
                submitBtn.disabled = false;
                if (formId === 'customerRegistrationForm') {
                    submitBtn.innerHTML = 'Create Customer Account';
                } else {
                    submitBtn.innerHTML = 'Submit for Verification';
                }
            }
        }

        // Show message
        function showMessage(message, type = 'error') {
            // Create or update alert
            let alertDiv = document.getElementById('messageAlert');
            if (!alertDiv) {
                alertDiv = document.createElement('div');
                alertDiv.id = 'messageAlert';
                alertDiv.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show mt-3`;
                alertDiv.innerHTML = `
                    <span id="messageText"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.registration-form') || document.querySelector('.container');
                container.insertBefore(alertDiv, container.firstChild);
            }

            alertDiv.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show mt-3`;
            document.getElementById('messageText').textContent = message;
            alertDiv.style.display = 'block';

            // Auto-hide after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(() => {
                    if (alertDiv) {
                        alertDiv.style.display = 'none';
                    }
                }, 5000);
            }
        }

        // API call helper
        async function apiCall(endpoint, method = 'GET', data = null) {
            const config = {
                method: method,
                headers: {},
                credentials: 'include'
            };

            if (data instanceof FormData) {
                config.body = data;
            } else if (data) {
                config.headers['Content-Type'] = 'application/json';
                config.body = JSON.stringify(data);
            }

            try {
                console.log('Making API call to:', `${API_BASE_URL}/${endpoint}`);
                console.log('With data:', data);

                const response = await fetch(`${API_BASE_URL}/${endpoint}`, config);

                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                console.log('Response content type:', contentType);
                console.log('Response status:', response.status);

                if (!contentType || !contentType.includes('application/json')) {
                    const textResponse = await response.text();
                    console.error('Non-JSON response:', textResponse);
                    throw new Error(`Server error: Expected JSON but got ${contentType}. Please check if the database is set up correctly.`);
                }

                const result = await response.json();
                console.log('API response:', result);

                if (!response.ok) {
                    throw new Error(result.error || result.message || 'An error occurred');
                }

                return result;
            } catch (error) {
                console.error('API Error:', error);
                if (error.message.includes('JSON')) {
                    throw new Error('Server configuration error. Please make sure the database is set up and XAMPP is running properly.');
                }
                throw error;
            }
        }

        function selectAccountType(type) {
            selectedAccountType = type;

            // Hide account type selection
            document.querySelectorAll('.account-type-card').forEach(card => {
                card.style.display = 'none';
            });

            // Show registration forms container
            document.getElementById('registrationForms').style.display = 'block';

            // Show appropriate form
            if (type === 'customer') {
                document.getElementById('customerForm').style.display = 'block';
                document.getElementById('contractorForm').style.display = 'none';
            } else {
                document.getElementById('contractorForm').style.display = 'block';
                document.getElementById('customerForm').style.display = 'none';
            }
        }

        function goBack() {
            // Hide registration forms
            document.getElementById('registrationForms').style.display = 'none';
            document.getElementById('customerForm').style.display = 'none';
            document.getElementById('contractorForm').style.display = 'none';

            // Show account type selection
            document.querySelectorAll('.account-type-card').forEach(card => {
                card.style.display = 'block';
            });

            selectedAccountType = '';
        }

        // Customer registration form handling
        document.getElementById('customerRegistrationForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            // Get form elements safely
            const firstNameEl = document.getElementById('customerFirstName');
            const lastNameEl = document.getElementById('customerLastName');
            const emailEl = document.getElementById('customerEmail');
            const phoneEl = document.getElementById('customerPhone');
            const districtEl = document.getElementById('customerDistrict');
            const passwordEl = document.getElementById('customerPassword');
            const confirmPasswordEl = document.getElementById('customerConfirmPassword');

            // Check if all elements exist
            if (!firstNameEl || !lastNameEl || !emailEl || !phoneEl || !districtEl || !passwordEl || !confirmPasswordEl) {
                showMessage('Form elements are missing. Please refresh the page and try again.');
                return;
            }

            const firstName = firstNameEl.value.trim();
            const lastName = lastNameEl.value.trim();
            const email = emailEl.value.trim();
            const phone = phoneEl.value.trim();
            const district = districtEl.value;
            const password = passwordEl.value;
            const confirmPassword = confirmPasswordEl.value;

            // Validation
            if (!firstName || !lastName || !email || !phone || !district || !password || !confirmPassword) {
                showMessage('Please fill in all required fields');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage('Please enter a valid email address');
                return;
            }

            if (password !== confirmPassword) {
                showMessage('Passwords do not match');
                return;
            }

            if (password.length < 6) {
                showMessage('Password must be at least 6 characters long');
                return;
            }

            showLoading('customerRegistrationForm', true);

            try {
                // Collect form data
                const formData = {
                    user_type: 'customer',
                    first_name: firstName,
                    last_name: lastName,
                    email: email,
                    phone: phone,
                    district: district,
                    password: password,
                    preferred_language: localStorage.getItem('preferredLanguage') || 'en'
                };

                console.log('Customer registration:', formData);

                // Call registration API
                const response = await apiCall('auth/register', 'POST', formData);

                if (response.success) {
                    showMessage('Registration successful! Redirecting to customer dashboard...', 'success');

                    // Clear form
                    document.getElementById('customerRegistrationForm').reset();

                    // Store user info in localStorage for dashboard
                    localStorage.setItem('userType', 'customer');
                    localStorage.setItem('userName', firstName + ' ' + lastName);
                    localStorage.setItem('userEmail', email);

                    // Redirect to customer dashboard after 2 seconds
                    setTimeout(() => {
                        window.location.href = 'customer-dashboard.html';
                    }, 2000);
                } else {
                    showMessage(response.error || 'Registration failed');
                }
            } catch (error) {
                showMessage(error.message || 'Registration failed. Please try again.');
            } finally {
                showLoading('customerRegistrationForm', false);
            }
        });

        // Contractor registration form handling
        document.getElementById('contractorRegistrationForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const password = document.getElementById('contractorPassword').value;
            const confirmPassword = document.getElementById('contractorConfirmPassword').value;
            const email = document.getElementById('contractorEmail').value.trim();
            const firstName = document.getElementById('contractorFirstName').value.trim();
            const lastName = document.getElementById('contractorLastName').value.trim();

            // Basic validation
            if (!email || !password || !confirmPassword || !firstName || !lastName) {
                showMessage('Please fill in all required fields');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage('Please enter a valid email address');
                return;
            }

            if (password !== confirmPassword) {
                showMessage('Passwords do not match');
                return;
            }

            if (password.length < 6) {
                showMessage('Password must be at least 6 characters long');
                return;
            }

            // Check if at least one service type is selected
            const serviceTypes = [];
            document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                if (checkbox.value && checkbox.value !== 'on') {
                    serviceTypes.push(checkbox.value);
                }
            });

            if (serviceTypes.length === 0) {
                showMessage('Please select at least one service type');
                return;
            }

            // Check if service areas are selected
            const serviceAreas = Array.from(document.getElementById('serviceAreas').selectedOptions).map(option => option.value);
            if (serviceAreas.length === 0) {
                showMessage('Please select at least one service area');
                return;
            }

            // Validate years of experience
            const yearsExperience = parseInt(document.getElementById('yearsExperience').value);
            if (yearsExperience < 1) {
                showMessage('Years of experience must be at least 1');
                return;
            }

            showLoading('contractorRegistrationForm', true);

            try {
                // Collect form data (simplified without file uploads for now)
                const formData = {
                    user_type: 'contractor',
                    first_name: firstName,
                    last_name: lastName,
                    email: email,
                    phone: document.getElementById('contractorPhone').value.trim(),
                    business_name: document.getElementById('businessName').value.trim(),
                    business_registration_number: document.getElementById('businessRegNumber').value.trim() || null,
                    years_experience: yearsExperience,
                    cida_registration_number: document.getElementById('cidaNumber').value.trim(),
                    cida_grade: document.getElementById('cidaGrade').value,
                    password: password,
                    services: serviceTypes,
                    service_areas: serviceAreas,
                    preferred_language: localStorage.getItem('preferredLanguage') || 'en'
                };

                console.log('Contractor registration:', formData);

                // Call registration API
                const response = await apiCall('auth/register', 'POST', formData);

                if (response.success) {
                    showMessage('Registration submitted successfully! Your account will be reviewed and verified by our admin team. Redirecting to contractor dashboard...', 'success');

                    // Clear form
                    document.getElementById('contractorRegistrationForm').reset();

                    // Store user info in localStorage for dashboard
                    localStorage.setItem('userType', 'contractor');
                    localStorage.setItem('userName', firstName + ' ' + lastName);
                    localStorage.setItem('userEmail', email);
                    localStorage.setItem('businessName', formData.business_name);

                    // Redirect to contractor dashboard after 3 seconds
                    setTimeout(() => {
                        window.location.href = 'contractor-dashboard.html';
                    }, 3000);
                } else {
                    showMessage(response.error || 'Registration failed');
                }
            } catch (error) {
                showMessage(error.message || 'Registration failed. Please try again.');
            } finally {
                showLoading('contractorRegistrationForm', false);
            }
        });

        // Language selector
        document.getElementById('languageSelect').addEventListener('change', function(e) {
            const selectedLang = e.target.value;
            localStorage.setItem('preferredLanguage', selectedLang);
            console.log('Language changed to:', selectedLang);
        });

        // File upload validation
        function validateFileUpload(input, maxSize = 5) {
            const file = input.files[0];
            if (file) {
                const fileSizeMB = file.size / (1024 * 1024);
                if (fileSizeMB > maxSize) {
                    alert(`File size must be less than ${maxSize}MB`);
                    input.value = '';
                    return false;
                }

                const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
                if (!allowedTypes.includes(file.type)) {
                    alert('Only PDF, JPG, and PNG files are allowed');
                    input.value = '';
                    return false;
                }
            }
            return true;
        }

        // Add file validation listeners
        document.getElementById('cidaDocument').addEventListener('change', function() {
            validateFileUpload(this);
        });

        document.getElementById('businessLicense').addEventListener('change', function() {
            validateFileUpload(this);
        });
    </script>

    <style>
        .account-type-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .account-type-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #FF6B35 !important;
        }

        .registration-form {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-check-input:checked {
            background-color: #FF6B35;
            border-color: #FF6B35;
        }

        .language-selector {
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background-color: #FF6B35;
            border-color: #FF6B35;
        }

        .btn-primary:hover {
            background-color: #e55a2b;
            border-color: #e55a2b;
        }

        .text-primary {
            color: #FF6B35 !important;
        }
    </style>

        // API call helper
        async function apiCall(endpoint, method = 'GET', data = null) {
            const config = {
                method: method,
                headers: {},
                credentials: 'include'
            };

            if (data instanceof FormData) {
                config.body = data;
            } else if (data) {
                config.headers['Content-Type'] = 'application/json';
                config.body = JSON.stringify(data);
            }

            try {
                console.log('Making API call to:', `${API_BASE_URL}/${endpoint}`);
                console.log('With data:', data);

                const response = await fetch(`${API_BASE_URL}/${endpoint}`, config);

                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                console.log('Response content type:', contentType);
                console.log('Response status:', response.status);

                if (!contentType || !contentType.includes('application/json')) {
                    const textResponse = await response.text();
                    console.error('Non-JSON response:', textResponse);
                    throw new Error(`Server error: Expected JSON but got ${contentType}. Please check if the database is set up correctly.`);
                }

                const result = await response.json();
                console.log('API response:', result);

                if (!response.ok) {
                    throw new Error(result.error || result.message || 'An error occurred');
                }

                return result;
            } catch (error) {
                console.error('API Error:', error);
                if (error.message.includes('JSON')) {
                    throw new Error('Server configuration error. Please make sure the database is set up and XAMPP is running properly.');
                }
                throw error;
            }
        }

        function selectAccountType(type) {
            selectedAccountType = type;

            // Hide account type selection
            document.querySelectorAll('.account-type-card').forEach(card => {
                card.style.display = 'none';
            });

            // Show registration forms container
            document.getElementById('registrationForms').style.display = 'block';

            // Show appropriate form
            if (type === 'customer') {
                document.getElementById('customerForm').style.display = 'block';
                document.getElementById('contractorForm').style.display = 'none';
            } else {
                document.getElementById('contractorForm').style.display = 'block';
                document.getElementById('customerForm').style.display = 'none';
            }
        }

        function goBack() {
            // Hide registration forms
            document.getElementById('registrationForms').style.display = 'none';
            document.getElementById('customerForm').style.display = 'none';
            document.getElementById('contractorForm').style.display = 'none';

            // Show account type selection
            document.querySelectorAll('.account-type-card').forEach(card => {
                card.style.display = 'block';
            });

            selectedAccountType = '';
        }

        // Customer registration form handling
        document.getElementById('customerRegistrationForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            // Get form elements safely
            const firstNameEl = document.getElementById('customerFirstName');
            const lastNameEl = document.getElementById('customerLastName');
            const emailEl = document.getElementById('customerEmail');
            const phoneEl = document.getElementById('customerPhone');
            const districtEl = document.getElementById('customerDistrict');
            const passwordEl = document.getElementById('customerPassword');
            const confirmPasswordEl = document.getElementById('customerConfirmPassword');

            // Check if all elements exist
            if (!firstNameEl || !lastNameEl || !emailEl || !phoneEl || !districtEl || !passwordEl || !confirmPasswordEl) {
                showMessage('Form elements are missing. Please refresh the page and try again.');
                return;
            }

            const firstName = firstNameEl.value.trim();
            const lastName = lastNameEl.value.trim();
            const email = emailEl.value.trim();
            const phone = phoneEl.value.trim();
            const district = districtEl.value;
            const password = passwordEl.value;
            const confirmPassword = confirmPasswordEl.value;

            // Validation
            if (!firstName || !lastName || !email || !phone || !district || !password || !confirmPassword) {
                showMessage('Please fill in all required fields');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage('Please enter a valid email address');
                return;
            }

            if (password !== confirmPassword) {
                showMessage('Passwords do not match');
                return;
            }

            if (password.length < 8) {
                showMessage('Password must be at least 8 characters long');
                return;
            }

            // Check password strength
            const hasUpperCase = /[A-Z]/.test(password);
            const hasLowerCase = /[a-z]/.test(password);
            const hasNumbers = /\d/.test(password);

            if (!hasUpperCase || !hasLowerCase || !hasNumbers) {
                showMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number');
                return;
            }

            showLoading('customerRegistrationForm', true);

            try {
                // Collect form data
                const formData = {
                    user_type: 'customer',
                    first_name: firstName,
                    last_name: lastName,
                    email: email,
                    phone: phone,
                    district: district,
                    password: password,
                    preferred_language: localStorage.getItem('preferredLanguage') || 'en'
                };

                console.log('Customer registration:', formData);

                // Call registration API
                const response = await apiCall('auth/register', 'POST', formData);

                if (response.success) {
                    showMessage('Registration successful! Please check your email for verification instructions.', 'success');

                    // Clear form
                    document.getElementById('customerRegistrationForm').reset();

                    // Redirect to login after 3 seconds
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 3000);
                } else {
                    showMessage(response.error || 'Registration failed');
                }
            } catch (error) {
                showMessage(error.message || 'Registration failed. Please try again.');
            } finally {
                showLoading('customerRegistrationForm', false);
            }
        });

        // Contractor registration form handling
        document.getElementById('contractorRegistrationForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const password = document.getElementById('contractorPassword').value;
            const confirmPassword = document.getElementById('contractorConfirmPassword').value;
            const email = document.getElementById('contractorEmail').value.trim();

            // Basic validation
            if (!email || !password || !confirmPassword) {
                showMessage('Please fill in all required fields');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage('Please enter a valid email address');
                return;
            }

            if (password !== confirmPassword) {
                showMessage('Passwords do not match');
                return;
            }

            if (password.length < 8) {
                showMessage('Password must be at least 8 characters long');
                return;
            }

            // Check password strength
            const hasUpperCase = /[A-Z]/.test(password);
            const hasLowerCase = /[a-z]/.test(password);
            const hasNumbers = /\d/.test(password);

            if (!hasUpperCase || !hasLowerCase || !hasNumbers) {
                showMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number');
                return;
            }

            // Check if at least one service type is selected
            const serviceTypes = [];
            document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                if (checkbox.value && checkbox.value !== 'on') {
                    serviceTypes.push(checkbox.value);
                }
            });

            if (serviceTypes.length === 0) {
                showMessage('Please select at least one service type');
                return;
            }

            // Check if service areas are selected
            const serviceAreas = Array.from(document.getElementById('serviceAreas').selectedOptions).map(option => option.value);
            if (serviceAreas.length === 0) {
                showMessage('Please select at least one service area');
                return;
            }

            // Validate years of experience
            const yearsExperience = parseInt(document.getElementById('yearsExperience').value);
            if (yearsExperience < 1) {
                showMessage('Years of experience must be at least 1');
                return;
            }

            // Check required documents
            const cidaDocument = document.getElementById('cidaDocument').files[0];
            if (!cidaDocument) {
                showMessage('CIDA registration document is required');
                return;
            }

            showLoading('contractorRegistrationForm', true);

            try {
                // First, upload documents if any
                let cidaDocumentPath = null;
                let businessLicensePath = null;

                if (cidaDocument) {
                    const cidaFormData = new FormData();
                    cidaFormData.append('file', cidaDocument);
                    cidaFormData.append('type', 'cida');

                    const cidaUploadResponse = await apiCall('upload', 'POST', cidaFormData);
                    if (cidaUploadResponse.success) {
                        cidaDocumentPath = cidaUploadResponse.data.files[0].file_path;
                    }
                }

                const businessLicense = document.getElementById('businessLicense').files[0];
                if (businessLicense) {
                    const licenseFormData = new FormData();
                    licenseFormData.append('file', businessLicense);
                    licenseFormData.append('type', 'license');

                    const licenseUploadResponse = await apiCall('upload', 'POST', licenseFormData);
                    if (licenseUploadResponse.success) {
                        businessLicensePath = licenseUploadResponse.data.files[0].file_path;
                    }
                }

                // Collect form data
                const formData = {
                    user_type: 'contractor',
                    first_name: document.getElementById('contractorFirstName').value.trim(),
                    last_name: document.getElementById('contractorLastName').value.trim(),
                    email: email,
                    phone: document.getElementById('contractorPhone').value.trim(),
                    business_name: document.getElementById('businessName').value.trim(),
                    business_registration_number: document.getElementById('businessRegNumber').value.trim() || null,
                    years_experience: yearsExperience,
                    cida_registration_number: document.getElementById('cidaNumber').value.trim(),
                    cida_grade: document.getElementById('cidaGrade').value,
                    password: password,
                    services: serviceTypes,
                    service_areas: serviceAreas,
                    cida_document_path: cidaDocumentPath,
                    business_license_path: businessLicensePath,
                    preferred_language: localStorage.getItem('preferredLanguage') || 'en'
                };

                console.log('Contractor registration:', formData);

                // Call registration API
                const response = await apiCall('auth/register', 'POST', formData);

                if (response.success) {
                    showMessage('Registration submitted successfully! Your account will be reviewed and verified by our admin team. You will receive an email notification once approved.', 'success');

                    // Clear form
                    document.getElementById('contractorRegistrationForm').reset();

                    // Redirect to login after 5 seconds
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 5000);
                } else {
                    showMessage(response.error || 'Registration failed');
                }
            } catch (error) {
                showMessage(error.message || 'Registration failed. Please try again.');
            } finally {
                showLoading('contractorRegistrationForm', false);
            }
        });

        // Language selector
        document.getElementById('languageSelect').addEventListener('change', function(e) {
            const selectedLang = e.target.value;
            console.log('Language changed to:', selectedLang);
        });

        // File upload validation
        function validateFileUpload(input, maxSize = 5) {
            const file = input.files[0];
            if (file) {
                const fileSizeMB = file.size / (1024 * 1024);
                if (fileSizeMB > maxSize) {
                    alert(`File size must be less than ${maxSize}MB`);
                    input.value = '';
                    return false;
                }

                const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
                if (!allowedTypes.includes(file.type)) {
                    alert('Only PDF, JPG, and PNG files are allowed');
                    input.value = '';
                    return false;
                }
            }
            return true;
        }

        // Add file validation listeners
        document.getElementById('cidaDocument').addEventListener('change', function() {
            validateFileUpload(this);
        });

        document.getElementById('businessLicense').addEventListener('change', function() {
            validateFileUpload(this);
        });
    </script>

    <style>
        .account-type-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .account-type-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: var(--accent-orange) !important;
        }

        .registration-form {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-check-input:checked {
            background-color: var(--accent-orange);
            border-color: var(--accent-orange);
        }

        .language-selector {
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</body>

</html>
