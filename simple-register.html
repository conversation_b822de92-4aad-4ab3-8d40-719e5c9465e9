<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Registration - Brick & Click</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        .registration-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .form-floating {
            margin-bottom: 15px;
        }
        .btn-primary {
            background: #FF6B35;
            border-color: #FF6B35;
        }
        .btn-primary:hover {
            background: #e55a2b;
            border-color: #e55a2b;
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <h2 class="text-center mb-4">Customer Registration</h2>
        
        <!-- Account Type Selection -->
        <div id="accountTypeSelection" class="text-center mb-4">
            <h5>Choose Account Type:</h5>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="selectAccountType('customer')">Customer</button>
                <button type="button" class="btn btn-outline-primary" onclick="selectAccountType('contractor')">Contractor</button>
            </div>
        </div>

        <!-- Customer Registration Form -->
        <div id="customerRegistrationForm" style="display: none;">
            <h4 class="text-primary mb-3">Customer Registration</h4>
            <form id="customerForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="firstName" placeholder="First Name" required>
                            <label for="firstName">First Name</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="lastName" placeholder="Last Name" required>
                            <label for="lastName">Last Name</label>
                        </div>
                    </div>
                </div>

                <div class="form-floating">
                    <input type="email" class="form-control" id="email" placeholder="Email" required>
                    <label for="email">Email Address</label>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="tel" class="form-control" id="phone" placeholder="Phone" required>
                            <label for="phone">Phone Number</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <select class="form-select" id="district" required>
                                <option value="">Select District</option>
                                <option value="colombo">Colombo</option>
                                <option value="gampaha">Gampaha</option>
                                <option value="kalutara">Kalutara</option>
                                <option value="kandy">Kandy</option>
                                <option value="matale">Matale</option>
                                <option value="galle">Galle</option>
                                <option value="kurunegala">Kurunegala</option>
                                <option value="anuradhapura">Anuradhapura</option>
                            </select>
                            <label for="district">District</label>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="password" class="form-control" id="password" placeholder="Password" required>
                            <label for="password">Password</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="password" class="form-control" id="confirmPassword" placeholder="Confirm Password" required>
                            <label for="confirmPassword">Confirm Password</label>
                        </div>
                    </div>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="terms" required>
                    <label class="form-check-label" for="terms">
                        I agree to the Terms and Conditions and Privacy Policy
                    </label>
                </div>

                <button type="submit" class="btn btn-primary w-100 py-2">Create Customer Account</button>
                <button type="button" class="btn btn-link w-100 mt-2" onclick="goBack()">← Choose Different Account Type</button>
            </form>
        </div>

        <!-- Contractor Registration Form -->
        <div id="contractorRegistrationForm" style="display: none;">
            <h4 class="text-primary mb-3">Contractor Registration</h4>
            <form id="contractorForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="contractorFirstName" placeholder="First Name" required>
                            <label for="contractorFirstName">First Name</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="contractorLastName" placeholder="Last Name" required>
                            <label for="contractorLastName">Last Name</label>
                        </div>
                    </div>
                </div>

                <div class="form-floating">
                    <input type="email" class="form-control" id="contractorEmail" placeholder="Email" required>
                    <label for="contractorEmail">Email Address</label>
                </div>

                <div class="form-floating">
                    <input type="tel" class="form-control" id="contractorPhone" placeholder="Phone" required>
                    <label for="contractorPhone">Phone Number</label>
                </div>

                <div class="form-floating">
                    <input type="text" class="form-control" id="businessName" placeholder="Business Name" required>
                    <label for="businessName">Business Name</label>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="number" class="form-control" id="yearsExperience" placeholder="Years" min="1" required>
                            <label for="yearsExperience">Years of Experience</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <select class="form-select" id="cidaGrade" required>
                                <option value="">Select CIDA Grade</option>
                                <option value="C1">C1</option>
                                <option value="C2">C2</option>
                                <option value="C3">C3</option>
                                <option value="C4">C4</option>
                                <option value="C5">C5</option>
                            </select>
                            <label for="cidaGrade">CIDA Grade</label>
                        </div>
                    </div>
                </div>

                <div class="form-floating">
                    <input type="text" class="form-control" id="cidaNumber" placeholder="CIDA Number" required>
                    <label for="cidaNumber">CIDA Registration Number</label>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="password" class="form-control" id="contractorPassword" placeholder="Password" required>
                            <label for="contractorPassword">Password</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="password" class="form-control" id="contractorConfirmPassword" placeholder="Confirm Password" required>
                            <label for="contractorConfirmPassword">Confirm Password</label>
                        </div>
                    </div>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="contractorTerms" required>
                    <label class="form-check-label" for="contractorTerms">
                        I agree to the Terms and Conditions and Privacy Policy
                    </label>
                </div>

                <button type="submit" class="btn btn-primary w-100 py-2">Submit for Verification</button>
                <button type="button" class="btn btn-link w-100 mt-2" onclick="goBack()">← Choose Different Account Type</button>
            </form>
        </div>

        <!-- Result Messages -->
        <div id="messageArea" class="mt-3"></div>

        <div class="text-center mt-4">
            <p>Already have an account? <a href="login.html" class="text-primary">Login here</a></p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE_URL = 'api';

        function selectAccountType(type) {
            document.getElementById('accountTypeSelection').style.display = 'none';
            
            if (type === 'customer') {
                document.getElementById('customerRegistrationForm').style.display = 'block';
                document.getElementById('contractorRegistrationForm').style.display = 'none';
            } else {
                document.getElementById('contractorRegistrationForm').style.display = 'block';
                document.getElementById('customerRegistrationForm').style.display = 'none';
            }
        }

        function goBack() {
            document.getElementById('accountTypeSelection').style.display = 'block';
            document.getElementById('customerRegistrationForm').style.display = 'none';
            document.getElementById('contractorRegistrationForm').style.display = 'none';
            clearMessages();
        }

        function showMessage(message, type = 'danger') {
            const messageArea = document.getElementById('messageArea');
            messageArea.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }

        function clearMessages() {
            document.getElementById('messageArea').innerHTML = '';
        }

        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const config = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                };

                if (data) {
                    config.body = JSON.stringify(data);
                }

                const response = await fetch(`${API_BASE_URL}/${endpoint}`, config);
                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || 'An error occurred');
                }

                return result;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }

        // Customer form submission
        document.getElementById('customerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            clearMessages();

            const formData = {
                user_type: 'customer',
                first_name: document.getElementById('firstName').value.trim(),
                last_name: document.getElementById('lastName').value.trim(),
                email: document.getElementById('email').value.trim(),
                phone: document.getElementById('phone').value.trim(),
                district: document.getElementById('district').value,
                password: document.getElementById('password').value,
                preferred_language: 'en'
            };

            // Basic validation
            if (!formData.first_name || !formData.last_name || !formData.email || !formData.phone || !formData.district || !formData.password) {
                showMessage('Please fill in all required fields');
                return;
            }

            const confirmPassword = document.getElementById('confirmPassword').value;
            if (formData.password !== confirmPassword) {
                showMessage('Passwords do not match');
                return;
            }

            if (formData.password.length < 6) {
                showMessage('Password must be at least 6 characters long');
                return;
            }

            try {
                const response = await apiCall('auth/register', 'POST', formData);
                
                if (response.success) {
                    showMessage('Registration successful! Please check your email for verification.', 'success');
                    document.getElementById('customerForm').reset();
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 3000);
                } else {
                    showMessage(response.error || 'Registration failed');
                }
            } catch (error) {
                showMessage(error.message || 'Registration failed. Please try again.');
            }
        });

        // Contractor form submission
        document.getElementById('contractorForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            clearMessages();

            const formData = {
                user_type: 'contractor',
                first_name: document.getElementById('contractorFirstName').value.trim(),
                last_name: document.getElementById('contractorLastName').value.trim(),
                email: document.getElementById('contractorEmail').value.trim(),
                phone: document.getElementById('contractorPhone').value.trim(),
                business_name: document.getElementById('businessName').value.trim(),
                years_experience: parseInt(document.getElementById('yearsExperience').value),
                cida_registration_number: document.getElementById('cidaNumber').value.trim(),
                cida_grade: document.getElementById('cidaGrade').value,
                password: document.getElementById('contractorPassword').value,
                preferred_language: 'en'
            };

            // Basic validation
            if (!formData.first_name || !formData.last_name || !formData.email || !formData.phone || 
                !formData.business_name || !formData.years_experience || !formData.cida_registration_number || 
                !formData.cida_grade || !formData.password) {
                showMessage('Please fill in all required fields');
                return;
            }

            const confirmPassword = document.getElementById('contractorConfirmPassword').value;
            if (formData.password !== confirmPassword) {
                showMessage('Passwords do not match');
                return;
            }

            if (formData.password.length < 6) {
                showMessage('Password must be at least 6 characters long');
                return;
            }

            try {
                const response = await apiCall('auth/register', 'POST', formData);
                
                if (response.success) {
                    showMessage('Registration submitted! Your account will be reviewed and verified by our admin team.', 'success');
                    document.getElementById('contractorForm').reset();
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 5000);
                } else {
                    showMessage(response.error || 'Registration failed');
                }
            } catch (error) {
                showMessage(error.message || 'Registration failed. Please try again.');
            }
        });
    </script>
</body>
</html>
