<?php
/**
 * Quote Controller
 * Handles quote requests and responses
 */

class QuoteController {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Create a new quote request
     */
    public function createQuoteRequest($customerId, $data) {
        validate_required_fields($data, [
            'title', 'description', 'service_category_id', 'project_type', 
            'district_id', 'project_location'
        ]);
        
        try {
            $this->db->beginTransaction();
            
            $sql = "INSERT INTO quote_requests (customer_id, title, description, service_category_id, 
                    project_type, budget_min, budget_max, project_location, district_id, 
                    project_start_date, floor_area, bedrooms, bathrooms, additional_requirements, 
                    urgency_level, preferred_contact_method) 
                    VALUES (:customer_id, :title, :description, :service_category_id, 
                    :project_type, :budget_min, :budget_max, :project_location, :district_id, 
                    :project_start_date, :floor_area, :bedrooms, :bathrooms, :additional_requirements, 
                    :urgency_level, :preferred_contact_method)";
            
            $quoteRequestId = $this->db->insert($sql, [
                ':customer_id' => $customerId,
                ':title' => $data['title'],
                ':description' => $data['description'],
                ':service_category_id' => $data['service_category_id'],
                ':project_type' => $data['project_type'],
                ':budget_min' => $data['budget_min'] ?? null,
                ':budget_max' => $data['budget_max'] ?? null,
                ':project_location' => $data['project_location'],
                ':district_id' => $data['district_id'],
                ':project_start_date' => $data['project_start_date'] ?? null,
                ':floor_area' => $data['floor_area'] ?? null,
                ':bedrooms' => $data['bedrooms'] ?? null,
                ':bathrooms' => $data['bathrooms'] ?? null,
                ':additional_requirements' => $data['additional_requirements'] ?? null,
                ':urgency_level' => $data['urgency_level'] ?? 'normal',
                ':preferred_contact_method' => $data['preferred_contact_method'] ?? 'email'
            ]);
            
            // Add attachments if any
            if (!empty($data['attachments']) && is_array($data['attachments'])) {
                foreach ($data['attachments'] as $attachment) {
                    $this->db->insert(
                        "INSERT INTO quote_request_attachments (quote_request_id, file_path, file_name, file_type) 
                         VALUES (:quote_request_id, :file_path, :file_name, :file_type)",
                        [
                            ':quote_request_id' => $quoteRequestId,
                            ':file_path' => $attachment['file_path'],
                            ':file_name' => $attachment['file_name'],
                            ':file_type' => $attachment['file_type']
                        ]
                    );
                }
            }
            
            $this->db->commit();
            
            // Log activity
            log_activity($customerId, 'quote_request_created', 'quote_request', $quoteRequestId, 'Quote request created');
            
            // Notify relevant contractors (implement in background)
            $this->notifyRelevantContractors($quoteRequestId, $data);
            
            return ['quote_request_id' => $quoteRequestId];
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Get quote requests for customer
     */
    public function getCustomerQuotes($customerId, $page = 1, $limit = 10) {
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT qr.*, sc.name as service_category, d.name as district_name,
                COUNT(qresp.id) as response_count,
                COUNT(CASE WHEN qresp.status = 'pending' THEN 1 END) as pending_responses,
                COUNT(CASE WHEN qresp.status = 'accepted' THEN 1 END) as accepted_responses
                FROM quote_requests qr
                JOIN service_categories sc ON qr.service_category_id = sc.id
                JOIN districts d ON qr.district_id = d.id
                LEFT JOIN quote_responses qresp ON qr.id = qresp.quote_request_id
                WHERE qr.customer_id = :customer_id
                GROUP BY qr.id
                ORDER BY qr.created_at DESC
                LIMIT :limit OFFSET :offset";
        
        $quotes = $this->db->fetchAll($sql, [
            ':customer_id' => $customerId,
            ':limit' => $limit,
            ':offset' => $offset
        ]);
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM quote_requests WHERE customer_id = :customer_id";
        $totalResult = $this->db->fetchOne($countSql, [':customer_id' => $customerId]);
        $total = $totalResult['total'];
        
        return [
            'quotes' => $quotes,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_items' => $total,
                'items_per_page' => $limit
            ]
        ];
    }
    
    /**
     * Get quote requests for contractors
     */
    public function getContractorQuotes($contractorId, $status = null, $page = 1, $limit = 10) {
        $offset = ($page - 1) * $limit;
        
        $conditions = ["qr.status = 'open'"];
        $params = [];
        
        // Filter by contractor's service areas and categories
        $sql = "SELECT qr.*, sc.name as service_category, d.name as district_name,
                CONCAT(u.first_name, ' ', u.last_name) as customer_name,
                u.phone as customer_phone,
                qresp.id as response_id,
                qresp.status as response_status,
                qresp.quoted_amount,
                qresp.created_at as response_date
                FROM quote_requests qr
                JOIN service_categories sc ON qr.service_category_id = sc.id
                JOIN districts d ON qr.district_id = d.id
                JOIN users u ON qr.customer_id = u.id
                LEFT JOIN quote_responses qresp ON qr.id = qresp.quote_request_id AND qresp.contractor_id = :contractor_id
                WHERE qr.service_category_id IN (
                    SELECT service_category_id FROM contractor_services WHERE contractor_id = :contractor_id
                )
                AND qr.district_id IN (
                    SELECT district_id FROM contractor_service_areas WHERE contractor_id = :contractor_id
                )";
        
        $params[':contractor_id'] = $contractorId;
        
        if ($status) {
            if ($status === 'responded') {
                $sql .= " AND qresp.id IS NOT NULL";
            } elseif ($status === 'not_responded') {
                $sql .= " AND qresp.id IS NULL";
            }
        }
        
        $sql .= " ORDER BY qr.created_at DESC LIMIT :limit OFFSET :offset";
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;
        
        $quotes = $this->db->fetchAll($sql, $params);
        
        return [
            'quotes' => $quotes,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => 1, // Simplified for now
                'total_items' => count($quotes),
                'items_per_page' => $limit
            ]
        ];
    }
    
    /**
     * Submit quote response
     */
    public function submitQuoteResponse($contractorId, $quoteRequestId, $data) {
        validate_required_fields($data, [
            'quoted_amount', 'estimated_duration_weeks', 'description'
        ]);
        
        // Check if contractor already responded
        $existingResponse = $this->db->fetchOne(
            "SELECT id FROM quote_responses WHERE quote_request_id = :quote_request_id AND contractor_id = :contractor_id",
            [':quote_request_id' => $quoteRequestId, ':contractor_id' => $contractorId]
        );
        
        if ($existingResponse) {
            throw new Exception("You have already responded to this quote request");
        }
        
        // Check if quote request is still open
        $quoteRequest = $this->db->fetchOne(
            "SELECT status, customer_id FROM quote_requests WHERE id = :id",
            [':id' => $quoteRequestId]
        );
        
        if (!$quoteRequest || $quoteRequest['status'] !== 'open') {
            throw new Exception("Quote request is no longer available");
        }
        
        try {
            $this->db->beginTransaction();
            
            $sql = "INSERT INTO quote_responses (quote_request_id, contractor_id, quoted_amount, 
                    estimated_duration_weeks, description, terms_and_conditions, warranty_period_months,
                    payment_terms, materials_included, labor_included, additional_costs) 
                    VALUES (:quote_request_id, :contractor_id, :quoted_amount, 
                    :estimated_duration_weeks, :description, :terms_and_conditions, :warranty_period_months,
                    :payment_terms, :materials_included, :labor_included, :additional_costs)";
            
            $responseId = $this->db->insert($sql, [
                ':quote_request_id' => $quoteRequestId,
                ':contractor_id' => $contractorId,
                ':quoted_amount' => $data['quoted_amount'],
                ':estimated_duration_weeks' => $data['estimated_duration_weeks'],
                ':description' => $data['description'],
                ':terms_and_conditions' => $data['terms_and_conditions'] ?? null,
                ':warranty_period_months' => $data['warranty_period_months'] ?? null,
                ':payment_terms' => $data['payment_terms'] ?? null,
                ':materials_included' => isset($data['materials_included']) ? (bool)$data['materials_included'] : true,
                ':labor_included' => isset($data['labor_included']) ? (bool)$data['labor_included'] : true,
                ':additional_costs' => $data['additional_costs'] ?? null
            ]);
            
            // Add breakdown items if provided
            if (!empty($data['breakdown']) && is_array($data['breakdown'])) {
                foreach ($data['breakdown'] as $item) {
                    $this->db->insert(
                        "INSERT INTO quote_response_breakdown (quote_response_id, item_description, quantity, unit_price, total_price) 
                         VALUES (:quote_response_id, :item_description, :quantity, :unit_price, :total_price)",
                        [
                            ':quote_response_id' => $responseId,
                            ':item_description' => $item['description'],
                            ':quantity' => $item['quantity'],
                            ':unit_price' => $item['unit_price'],
                            ':total_price' => $item['total_price']
                        ]
                    );
                }
            }
            
            $this->db->commit();
            
            // Get contractor info for notification
            $contractor = $this->db->fetchOne(
                "SELECT u.first_name, u.last_name, cp.business_name 
                 FROM users u 
                 JOIN contractor_profiles cp ON u.id = cp.user_id 
                 WHERE cp.id = :contractor_id",
                [':contractor_id' => $contractorId]
            );
            
            // Send notification to customer
            NotificationHelper::quoteReceived(
                $quoteRequest['customer_id'],
                $contractor['business_name'],
                $responseId
            );
            
            // Log activity
            log_activity($contractorId, 'quote_response_submitted', 'quote_response', $responseId, 'Quote response submitted');
            
            return ['quote_response_id' => $responseId];
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Accept quote response
     */
    public function acceptQuoteResponse($customerId, $responseId) {
        // Get quote response details
        $response = $this->db->fetchOne(
            "SELECT qr.*, qreq.customer_id, qreq.title as project_title
             FROM quote_responses qr
             JOIN quote_requests qreq ON qr.quote_request_id = qreq.id
             WHERE qr.id = :response_id AND qreq.customer_id = :customer_id",
            [':response_id' => $responseId, ':customer_id' => $customerId]
        );
        
        if (!$response) {
            throw new Exception("Quote response not found");
        }
        
        if ($response['status'] !== 'pending') {
            throw new Exception("Quote response is no longer available");
        }
        
        try {
            $this->db->beginTransaction();
            
            // Update quote response status
            $this->db->update(
                "UPDATE quote_responses SET status = 'accepted', accepted_at = NOW() WHERE id = :id",
                [':id' => $responseId]
            );
            
            // Close the quote request
            $this->db->update(
                "UPDATE quote_requests SET status = 'closed' WHERE id = :id",
                [':id' => $response['quote_request_id']]
            );
            
            // Reject other responses for the same quote request
            $this->db->update(
                "UPDATE quote_responses SET status = 'rejected' 
                 WHERE quote_request_id = :quote_request_id AND id != :accepted_id",
                [
                    ':quote_request_id' => $response['quote_request_id'],
                    ':accepted_id' => $responseId
                ]
            );
            
            // Create project
            $projectController = new ProjectController();
            $projectId = $projectController->createProjectFromQuote($responseId);
            
            $this->db->commit();
            
            // Get contractor user ID for notification
            $contractor = $this->db->fetchOne(
                "SELECT cp.user_id, u.first_name, u.last_name 
                 FROM contractor_profiles cp 
                 JOIN users u ON cp.user_id = u.id 
                 WHERE cp.id = :contractor_id",
                [':contractor_id' => $response['contractor_id']]
            );
            
            // Get customer name for notification
            $customer = $this->db->fetchOne(
                "SELECT first_name, last_name FROM users WHERE id = :customer_id",
                [':customer_id' => $customerId]
            );
            
            // Send notification to contractor
            NotificationHelper::quoteAccepted(
                $contractor['user_id'],
                $customer['first_name'] . ' ' . $customer['last_name'],
                $responseId
            );
            
            // Log activity
            log_activity($customerId, 'quote_accepted', 'quote_response', $responseId, 'Quote response accepted');
            
            return ['project_id' => $projectId];
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Get quote response details
     */
    public function getQuoteResponseDetails($responseId, $userId, $userType) {
        $sql = "SELECT qr.*, qreq.title, qreq.description as request_description,
                qreq.project_location, qreq.budget_min, qreq.budget_max,
                sc.name as service_category, d.name as district_name,
                cp.business_name, cp.years_experience, cp.cida_grade,
                CONCAT(cu.first_name, ' ', cu.last_name) as customer_name,
                CONCAT(conu.first_name, ' ', conu.last_name) as contractor_name
                FROM quote_responses qr
                JOIN quote_requests qreq ON qr.quote_request_id = qreq.id
                JOIN service_categories sc ON qreq.service_category_id = sc.id
                JOIN districts d ON qreq.district_id = d.id
                JOIN contractor_profiles cp ON qr.contractor_id = cp.id
                JOIN users cu ON qreq.customer_id = cu.id
                JOIN users conu ON cp.user_id = conu.id
                WHERE qr.id = :response_id";
        
        // Add user-specific conditions
        if ($userType === 'customer') {
            $sql .= " AND qreq.customer_id = :user_id";
        } elseif ($userType === 'contractor') {
            $sql .= " AND cp.user_id = :user_id";
        }
        
        $response = $this->db->fetchOne($sql, [
            ':response_id' => $responseId,
            ':user_id' => $userId
        ]);
        
        if (!$response) {
            throw new Exception("Quote response not found");
        }
        
        // Get breakdown items
        $breakdown = $this->db->fetchAll(
            "SELECT * FROM quote_response_breakdown WHERE quote_response_id = :response_id ORDER BY id",
            [':response_id' => $responseId]
        );
        
        $response['breakdown'] = $breakdown;
        
        return $response;
    }
    
    /**
     * Notify relevant contractors about new quote request
     */
    private function notifyRelevantContractors($quoteRequestId, $data) {
        // Get contractors who serve the area and category
        $contractors = $this->db->fetchAll(
            "SELECT DISTINCT cp.user_id, cp.business_name
             FROM contractor_profiles cp
             JOIN contractor_services cs ON cp.id = cs.contractor_id
             JOIN contractor_service_areas csa ON cp.id = csa.contractor_id
             WHERE cs.service_category_id = :service_category_id
             AND csa.district_id = :district_id
             AND cp.verification_status = 'approved'",
            [
                ':service_category_id' => $data['service_category_id'],
                ':district_id' => $data['district_id']
            ]
        );
        
        // Create notifications for relevant contractors
        $notificationController = new NotificationController();
        foreach ($contractors as $contractor) {
            $notificationController->createNotification(
                $contractor['user_id'],
                'quote_request',
                'New Quote Request Available',
                "A new quote request for '{$data['title']}' is available in your service area",
                $quoteRequestId,
                'quote_request',
                "/contractor-quotes.html?id={$quoteRequestId}"
            );
        }
    }
}
?>
