<?php
/**
 * Analytics Controller
 * Handles analytics and reporting
 */

class AnalyticsController {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get platform overview analytics
     */
    public function getPlatformOverview($startDate = null, $endDate = null) {
        $startDate = $startDate ?: date('Y-m-01'); // First day of current month
        $endDate = $endDate ?: date('Y-m-d'); // Today
        
        // User statistics
        $userStats = $this->db->fetchOne(
            "SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN user_type = 'customer' THEN 1 END) as total_customers,
                COUNT(CASE WHEN user_type = 'contractor' THEN 1 END) as total_contractors,
                COUNT(CASE WHEN created_at BETWEEN :start_date AND :end_date THEN 1 END) as new_users_period,
                COUNT(CASE WHEN last_login_at BETWEEN :start_date AND :end_date THEN 1 END) as active_users_period
             FROM users",
            [':start_date' => $startDate, ':end_date' => $endDate . ' 23:59:59']
        );
        
        // Project statistics
        $projectStats = $this->db->fetchOne(
            "SELECT 
                COUNT(*) as total_projects,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_projects,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_projects,
                COUNT(CASE WHEN created_at BETWEEN :start_date AND :end_date THEN 1 END) as new_projects_period,
                SUM(total_amount) as total_project_value,
                SUM(CASE WHEN created_at BETWEEN :start_date AND :end_date THEN total_amount ELSE 0 END) as project_value_period
             FROM projects",
            [':start_date' => $startDate, ':end_date' => $endDate . ' 23:59:59']
        );
        
        // Quote statistics
        $quoteStats = $this->db->fetchOne(
            "SELECT 
                COUNT(*) as total_quote_requests,
                COUNT(CASE WHEN status = 'open' THEN 1 END) as open_quotes,
                COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed_quotes,
                COUNT(CASE WHEN created_at BETWEEN :start_date AND :end_date THEN 1 END) as new_quotes_period
             FROM quote_requests",
            [':start_date' => $startDate, ':end_date' => $endDate . ' 23:59:59']
        );
        
        // Payment statistics
        $paymentStats = $this->db->fetchOne(
            "SELECT 
                COUNT(*) as total_payments,
                COUNT(CASE WHEN pp.status = 'paid' THEN 1 END) as completed_payments,
                SUM(pp.amount) as total_payment_amount,
                SUM(CASE WHEN pp.status = 'paid' THEN pp.amount ELSE 0 END) as total_paid_amount,
                SUM(CASE WHEN pp.paid_at BETWEEN :start_date AND :end_date THEN pp.amount ELSE 0 END) as payments_period
             FROM project_payments pp",
            [':start_date' => $startDate, ':end_date' => $endDate . ' 23:59:59']
        );
        
        // Review statistics
        $reviewStats = $this->db->fetchOne(
            "SELECT 
                COUNT(*) as total_reviews,
                AVG(rating) as average_rating,
                COUNT(CASE WHEN created_at BETWEEN :start_date AND :end_date THEN 1 END) as new_reviews_period
             FROM reviews WHERE status = 'approved'",
            [':start_date' => $startDate, ':end_date' => $endDate . ' 23:59:59']
        );
        
        return [
            'users' => $userStats,
            'projects' => $projectStats,
            'quotes' => $quoteStats,
            'payments' => $paymentStats,
            'reviews' => $reviewStats,
            'period' => ['start_date' => $startDate, 'end_date' => $endDate]
        ];
    }
    
    /**
     * Get user growth analytics
     */
    public function getUserGrowthAnalytics($period = '30_days') {
        $days = $this->getPeriodDays($period);
        
        $sql = "SELECT 
                DATE(created_at) as date,
                COUNT(*) as total_registrations,
                COUNT(CASE WHEN user_type = 'customer' THEN 1 END) as customer_registrations,
                COUNT(CASE WHEN user_type = 'contractor' THEN 1 END) as contractor_registrations
                FROM users 
                WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
                GROUP BY DATE(created_at)
                ORDER BY date";
        
        return $this->db->fetchAll($sql, [':days' => $days]);
    }
    
    /**
     * Get project analytics
     */
    public function getProjectAnalytics($period = '30_days') {
        $days = $this->getPeriodDays($period);
        
        // Project creation trends
        $creationTrends = $this->db->fetchAll(
            "SELECT 
                DATE(created_at) as date,
                COUNT(*) as projects_created,
                SUM(total_amount) as total_value
             FROM projects 
             WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
             GROUP BY DATE(created_at)
             ORDER BY date",
            [':days' => $days]
        );
        
        // Project completion trends
        $completionTrends = $this->db->fetchAll(
            "SELECT 
                DATE(completed_at) as date,
                COUNT(*) as projects_completed
             FROM projects 
             WHERE completed_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
             GROUP BY DATE(completed_at)
             ORDER BY date",
            [':days' => $days]
        );
        
        // Projects by service category
        $byCategory = $this->db->fetchAll(
            "SELECT 
                sc.name as category,
                COUNT(*) as project_count,
                SUM(p.total_amount) as total_value
             FROM projects p
             JOIN service_categories sc ON p.service_category_id = sc.id
             WHERE p.created_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
             GROUP BY sc.id, sc.name
             ORDER BY project_count DESC",
            [':days' => $days]
        );
        
        // Projects by district
        $byDistrict = $this->db->fetchAll(
            "SELECT 
                d.name as district,
                COUNT(*) as project_count,
                SUM(p.total_amount) as total_value
             FROM projects p
             JOIN districts d ON p.district_id = d.id
             WHERE p.created_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
             GROUP BY d.id, d.name
             ORDER BY project_count DESC
             LIMIT 10",
            [':days' => $days]
        );
        
        return [
            'creation_trends' => $creationTrends,
            'completion_trends' => $completionTrends,
            'by_category' => $byCategory,
            'by_district' => $byDistrict
        ];
    }
    
    /**
     * Get contractor performance analytics
     */
    public function getContractorAnalytics($contractorId = null, $period = '30_days') {
        $days = $this->getPeriodDays($period);
        
        $conditions = ["p.created_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)"];
        $params = [':days' => $days];
        
        if ($contractorId) {
            $conditions[] = "p.contractor_id = :contractor_id";
            $params[':contractor_id'] = $contractorId;
        }
        
        $whereClause = "WHERE " . implode(" AND ", $conditions);
        
        // Overall performance
        $performance = $this->db->fetchOne(
            "SELECT 
                COUNT(*) as total_projects,
                COUNT(CASE WHEN p.status = 'completed' THEN 1 END) as completed_projects,
                AVG(CASE WHEN p.status = 'completed' THEN DATEDIFF(p.completed_at, p.start_date) END) as avg_completion_days,
                SUM(p.total_amount) as total_earnings,
                AVG(r.rating) as average_rating,
                COUNT(r.id) as total_reviews
             FROM projects p
             LEFT JOIN reviews r ON p.id = r.project_id AND r.status = 'approved'
             $whereClause",
            $params
        );
        
        // Top performing contractors (if not specific contractor)
        $topContractors = [];
        if (!$contractorId) {
            $topContractors = $this->db->fetchAll(
                "SELECT 
                    cp.business_name,
                    CONCAT(u.first_name, ' ', u.last_name) as contractor_name,
                    COUNT(p.id) as project_count,
                    SUM(p.total_amount) as total_earnings,
                    AVG(r.rating) as average_rating,
                    COUNT(r.id) as review_count
                 FROM contractor_profiles cp
                 JOIN users u ON cp.user_id = u.id
                 JOIN projects p ON cp.id = p.contractor_id
                 LEFT JOIN reviews r ON p.id = r.project_id AND r.status = 'approved'
                 $whereClause
                 GROUP BY cp.id
                 ORDER BY total_earnings DESC
                 LIMIT 10",
                $params
            );
        }
        
        return [
            'performance' => $performance,
            'top_contractors' => $topContractors
        ];
    }
    
    /**
     * Get financial analytics
     */
    public function getFinancialAnalytics($period = '30_days') {
        $days = $this->getPeriodDays($period);
        
        // Payment trends
        $paymentTrends = $this->db->fetchAll(
            "SELECT 
                DATE(pp.paid_at) as date,
                COUNT(*) as payments_count,
                SUM(pp.amount) as total_amount
             FROM project_payments pp
             WHERE pp.paid_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
             AND pp.status = 'paid'
             GROUP BY DATE(pp.paid_at)
             ORDER BY date",
            [':days' => $days]
        );
        
        // Payment methods distribution
        $paymentMethods = $this->db->fetchAll(
            "SELECT 
                pt.payment_method,
                COUNT(*) as transaction_count,
                SUM(pt.amount) as total_amount
             FROM payment_transactions pt
             WHERE pt.completed_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
             AND pt.status = 'completed'
             GROUP BY pt.payment_method
             ORDER BY total_amount DESC"
        );
        
        // Revenue by service category
        $revenueByCategory = $this->db->fetchAll(
            "SELECT 
                sc.name as category,
                SUM(pp.amount) as revenue,
                COUNT(pp.id) as payment_count
             FROM project_payments pp
             JOIN projects p ON pp.project_id = p.id
             JOIN service_categories sc ON p.service_category_id = sc.id
             WHERE pp.paid_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
             AND pp.status = 'paid'
             GROUP BY sc.id, sc.name
             ORDER BY revenue DESC",
            [':days' => $days]
        );
        
        return [
            'payment_trends' => $paymentTrends,
            'payment_methods' => $paymentMethods,
            'revenue_by_category' => $revenueByCategory
        ];
    }
    
    /**
     * Get customer analytics
     */
    public function getCustomerAnalytics($customerId = null, $period = '30_days') {
        $days = $this->getPeriodDays($period);
        
        $conditions = ["p.created_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)"];
        $params = [':days' => $days];
        
        if ($customerId) {
            $conditions[] = "p.customer_id = :customer_id";
            $params[':customer_id'] = $customerId;
        }
        
        $whereClause = "WHERE " . implode(" AND ", $conditions);
        
        // Customer activity
        $activity = $this->db->fetchOne(
            "SELECT 
                COUNT(DISTINCT p.customer_id) as active_customers,
                COUNT(p.id) as total_projects,
                SUM(p.total_amount) as total_spending,
                AVG(p.total_amount) as avg_project_value
             FROM projects p
             $whereClause",
            $params
        );
        
        // Top spending customers (if not specific customer)
        $topCustomers = [];
        if (!$customerId) {
            $topCustomers = $this->db->fetchAll(
                "SELECT 
                    CONCAT(u.first_name, ' ', u.last_name) as customer_name,
                    u.email,
                    COUNT(p.id) as project_count,
                    SUM(p.total_amount) as total_spending,
                    AVG(p.total_amount) as avg_project_value
                 FROM users u
                 JOIN projects p ON u.id = p.customer_id
                 $whereClause
                 GROUP BY u.id
                 ORDER BY total_spending DESC
                 LIMIT 10",
                $params
            );
        }
        
        return [
            'activity' => $activity,
            'top_customers' => $topCustomers
        ];
    }
    
    /**
     * Get quote conversion analytics
     */
    public function getQuoteConversionAnalytics($period = '30_days') {
        $days = $this->getPeriodDays($period);
        
        // Quote conversion rates
        $conversion = $this->db->fetchOne(
            "SELECT 
                COUNT(qr.id) as total_quote_requests,
                COUNT(CASE WHEN qr.status = 'closed' THEN 1 END) as closed_quotes,
                COUNT(qresp.id) as total_responses,
                COUNT(CASE WHEN qresp.status = 'accepted' THEN 1 END) as accepted_responses,
                AVG(response_count.responses_per_quote) as avg_responses_per_quote
             FROM quote_requests qr
             LEFT JOIN quote_responses qresp ON qr.id = qresp.quote_request_id
             LEFT JOIN (
                 SELECT quote_request_id, COUNT(*) as responses_per_quote
                 FROM quote_responses
                 GROUP BY quote_request_id
             ) response_count ON qr.id = response_count.quote_request_id
             WHERE qr.created_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)",
            [':days' => $days]
        );
        
        // Calculate conversion rates
        if ($conversion['total_quote_requests'] > 0) {
            $conversion['quote_closure_rate'] = round(($conversion['closed_quotes'] / $conversion['total_quote_requests']) * 100, 2);
        } else {
            $conversion['quote_closure_rate'] = 0;
        }
        
        if ($conversion['total_responses'] > 0) {
            $conversion['response_acceptance_rate'] = round(($conversion['accepted_responses'] / $conversion['total_responses']) * 100, 2);
        } else {
            $conversion['response_acceptance_rate'] = 0;
        }
        
        return $conversion;
    }
    
    /**
     * Get system health metrics
     */
    public function getSystemHealthMetrics() {
        // Database performance metrics
        $dbMetrics = $this->db->fetchOne(
            "SELECT 
                COUNT(*) as total_records,
                (SELECT COUNT(*) FROM users) as user_count,
                (SELECT COUNT(*) FROM projects) as project_count,
                (SELECT COUNT(*) FROM quote_requests) as quote_count,
                (SELECT COUNT(*) FROM reviews) as review_count"
        );
        
        // Recent activity metrics
        $activityMetrics = $this->db->fetchOne(
            "SELECT 
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as activity_last_hour,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as activity_last_24h,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as activity_last_7d
             FROM activity_logs"
        );
        
        // Error metrics (if you have error logging)
        $errorMetrics = [
            'errors_last_hour' => 0,
            'errors_last_24h' => 0,
            'critical_errors' => 0
        ];
        
        return [
            'database' => $dbMetrics,
            'activity' => $activityMetrics,
            'errors' => $errorMetrics,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Generate custom report
     */
    public function generateCustomReport($reportConfig) {
        $reportType = $reportConfig['type'];
        $startDate = $reportConfig['start_date'] ?? date('Y-m-01');
        $endDate = $reportConfig['end_date'] ?? date('Y-m-d');
        $filters = $reportConfig['filters'] ?? [];
        
        switch ($reportType) {
            case 'user_activity':
                return $this->generateUserActivityReport($startDate, $endDate, $filters);
            case 'financial_summary':
                return $this->generateFinancialSummaryReport($startDate, $endDate, $filters);
            case 'contractor_performance':
                return $this->generateContractorPerformanceReport($startDate, $endDate, $filters);
            case 'project_analysis':
                return $this->generateProjectAnalysisReport($startDate, $endDate, $filters);
            default:
                throw new Exception("Invalid report type");
        }
    }
    
    /**
     * Get period days from period string
     */
    private function getPeriodDays($period) {
        switch ($period) {
            case '7_days':
                return 7;
            case '30_days':
                return 30;
            case '90_days':
                return 90;
            case '1_year':
                return 365;
            default:
                return 30;
        }
    }
    
    /**
     * Generate user activity report
     */
    private function generateUserActivityReport($startDate, $endDate, $filters) {
        // Implementation for user activity report
        return [];
    }
    
    /**
     * Generate financial summary report
     */
    private function generateFinancialSummaryReport($startDate, $endDate, $filters) {
        // Implementation for financial summary report
        return [];
    }
    
    /**
     * Generate contractor performance report
     */
    private function generateContractorPerformanceReport($startDate, $endDate, $filters) {
        // Implementation for contractor performance report
        return [];
    }
    
    /**
     * Generate project analysis report
     */
    private function generateProjectAnalysisReport($startDate, $endDate, $filters) {
        // Implementation for project analysis report
        return [];
    }
}
?>
