<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Admin <PERSON>gin - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="admin login, construction management, Sri Lanka" name="keywords">
    <meta content="Administrator Login - Brick & Click Management Portal" name="description">

    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">

    <style>
        .admin-login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .admin-login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .admin-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .admin-header .admin-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
        }

        .admin-header .admin-icon i {
            font-size: 35px;
            color: white;
        }

        .admin-header h2 {
            color: #333;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .admin-header p {
            color: #666;
            font-size: 14px;
        }

        .form-floating {
            margin-bottom: 20px;
        }

        .form-floating input {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            transition: all 0.3s ease;
        }

        .form-floating input:focus {
            border-color: #FF6B35;
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }

        .btn-admin-login {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            border: none;
            border-radius: 10px;
            padding: 15px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }

        .btn-admin-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
        }

        .security-notice {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            text-align: center;
        }

        .security-notice i {
            color: #ffc107;
            margin-right: 8px;
        }

        .security-notice small {
            color: #856404;
            font-size: 12px;
        }

        .back-to-site {
            text-align: center;
            margin-top: 30px;
        }

        .back-to-site a {
            color: #666;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .back-to-site a:hover {
            color: #FF6B35;
        }

        .loading-spinner {
            display: none;
            margin-right: 10px;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #666;
            z-index: 10;
        }

        .password-field {
            position: relative;
        }

        .login-attempts {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            border-radius: 10px;
            padding: 10px;
            margin-top: 15px;
            text-align: center;
            display: none;
        }

        .login-attempts small {
            color: #dc3545;
            font-size: 12px;
        }
    </style>
</head>

<body>
    <div class="admin-login-container">
        <div class="admin-login-card">
            <div class="admin-header">
                <div class="admin-icon">
                    <i class="fas fa-user-shield"></i>
                </div>
                <h2>Administrator Portal</h2>
                <p>Secure access to Brick & Click management system</p>
            </div>

            <!-- Login Form -->
            <form id="adminLoginForm">
                <div class="form-floating">
                    <input type="email" class="form-control" id="adminEmail" placeholder="<EMAIL>" required>
                    <label for="adminEmail"><i class="fas fa-envelope me-2"></i>Administrator Email</label>
                </div>

                <div class="form-floating password-field">
                    <input type="password" class="form-control" id="adminPassword" placeholder="Password" required>
                    <label for="adminPassword"><i class="fas fa-lock me-2"></i>Password</label>
                    <span class="password-toggle" onclick="togglePassword()">
                        <i class="fas fa-eye" id="passwordToggleIcon"></i>
                    </span>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="rememberMe">
                    <label class="form-check-label" for="rememberMe">
                        Keep me signed in for 24 hours
                    </label>
                </div>

                <button type="submit" class="btn btn-primary btn-admin-login">
                    <span class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                    </span>
                    <span class="btn-text">Access Admin Panel</span>
                </button>

                <!-- Login Attempts Warning -->
                <div class="login-attempts" id="loginAttempts">
                    <i class="fas fa-exclamation-triangle"></i>
                    <small id="attemptsText">Multiple failed login attempts detected. Please wait before trying again.</small>
                </div>
            </form>

            <!-- Security Notice -->
            <div class="security-notice">
                <i class="fas fa-shield-alt"></i>
                <small>This is a secure administrator area. All login attempts are monitored and logged.</small>
            </div>

            <!-- Back to Site -->
            <div class="back-to-site">
                <a href="index.html">
                    <i class="fas fa-arrow-left me-1"></i>
                    Back to Main Site
                </a>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // API Configuration
        const API_BASE_URL = 'api/';
        let loginAttempts = parseInt(localStorage.getItem('adminLoginAttempts') || '0');
        let lastAttemptTime = parseInt(localStorage.getItem('adminLastAttempt') || '0');

        // Check for existing admin session on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkExistingSession();
            checkLoginAttempts();
        });

        // Check if admin is already logged in
        function checkExistingSession() {
            const user = localStorage.getItem('user');
            const loginTime = localStorage.getItem('loginTime');

            if (user && loginTime) {
                const userData = JSON.parse(user);
                const timeDiff = Date.now() - parseInt(loginTime);
                const oneHour = 60 * 60 * 1000; // 1 hour

                if (timeDiff < oneHour && userData.user_type === 'admin') {
                    window.location.href = 'admin-dashboard.html';
                }
            }
        }

        // Check login attempts and show warning if needed
        function checkLoginAttempts() {
            const now = Date.now();
            const lockoutTime = 15 * 60 * 1000; // 15 minutes

            if (loginAttempts >= 3 && (now - lastAttemptTime) < lockoutTime) {
                const remainingTime = Math.ceil((lockoutTime - (now - lastAttemptTime)) / 60000);
                document.getElementById('loginAttempts').style.display = 'block';
                document.getElementById('attemptsText').textContent =
                    `Too many failed attempts. Please wait ${remainingTime} minutes before trying again.`;
                document.getElementById('adminLoginForm').style.opacity = '0.5';
                document.querySelector('.btn-admin-login').disabled = true;
            } else if (loginAttempts >= 3) {
                // Reset attempts after lockout period
                localStorage.removeItem('adminLoginAttempts');
                localStorage.removeItem('adminLastAttempt');
                loginAttempts = 0;
            }
        }

        // Toggle password visibility
        function togglePassword() {
            const passwordField = document.getElementById('adminPassword');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // API Call function
        async function apiCall(endpoint, method = 'GET', data = null) {
            const url = API_BASE_URL + endpoint;
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include'
            };

            if (data) {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(url, options);

                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error(`Server returned non-JSON response: ${response.status} ${response.statusText}`);
                }

                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || result.message || `HTTP ${response.status}: ${response.statusText}`);
                }

                return result;
            } catch (error) {
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    throw new Error('Network error: Unable to connect to server. Please check if the server is running.');
                }
                throw error;
            }
        }

        // Show loading state
        function showLoading(show) {
            const spinner = document.querySelector('.loading-spinner');
            const btnText = document.querySelector('.btn-text');
            const submitBtn = document.querySelector('.btn-admin-login');

            if (show) {
                spinner.style.display = 'inline-block';
                btnText.textContent = 'Authenticating...';
                submitBtn.disabled = true;
            } else {
                spinner.style.display = 'none';
                btnText.textContent = 'Access Admin Panel';
                submitBtn.disabled = false;
            }
        }

        // Show error message
        function showError(message) {
            // Remove existing alerts
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
            alertDiv.innerHTML = `
                <i class="fas fa-exclamation-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.getElementById('adminLoginForm').insertBefore(alertDiv, document.querySelector('.btn-admin-login'));

            // Auto-hide after 5 seconds
            setTimeout(() => {
                if (alertDiv && alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Show success message
        function showSuccess(message) {
            // Remove existing alerts
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.getElementById('adminLoginForm').insertBefore(alertDiv, document.querySelector('.btn-admin-login'));
        }

        // Handle form submission
        document.getElementById('adminLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            // Check if locked out
            const now = Date.now();
            const lockoutTime = 15 * 60 * 1000; // 15 minutes

            if (loginAttempts >= 3 && (now - lastAttemptTime) < lockoutTime) {
                const remainingTime = Math.ceil((lockoutTime - (now - lastAttemptTime)) / 60000);
                showError(`Too many failed attempts. Please wait ${remainingTime} minutes before trying again.`);
                return;
            }

            const email = document.getElementById('adminEmail').value.trim();
            const password = document.getElementById('adminPassword').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            // Basic validation
            if (!email || !password) {
                showError('Please enter both email and password');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showError('Please enter a valid email address');
                return;
            }

            showLoading(true);

            try {
                // Call admin login API with enhanced security
                const response = await apiCall('auth/admin-login', 'POST', {
                    email: email,
                    password: password
                });

                if (response.success && response.data) {
                    const user = response.data;

                    // Verify user is admin
                    if (user.user_type !== 'admin') {
                        throw new Error('Access denied. Administrator privileges required.');
                    }

                    // Reset login attempts on successful login
                    localStorage.removeItem('adminLoginAttempts');
                    localStorage.removeItem('adminLastAttempt');

                    // Store user data
                    localStorage.setItem('user', JSON.stringify(user));
                    localStorage.setItem('loginTime', Date.now().toString());

                    // Set extended session if remember me is checked
                    if (rememberMe) {
                        localStorage.setItem('extendedSession', 'true');
                    }

                    showSuccess('Login successful! Redirecting to admin dashboard...');

                    // Log successful admin login
                    console.log('Admin login successful:', {
                        user: user.email,
                        time: new Date().toISOString()
                    });

                    // Redirect to admin dashboard
                    setTimeout(() => {
                        window.location.href = 'admin-dashboard.html';
                    }, 1500);

                } else {
                    throw new Error(response.error || 'Login failed');
                }

            } catch (error) {
                // Increment login attempts
                loginAttempts++;
                lastAttemptTime = Date.now();
                localStorage.setItem('adminLoginAttempts', loginAttempts.toString());
                localStorage.setItem('adminLastAttempt', lastAttemptTime.toString());

                // Show appropriate error message
                if (loginAttempts >= 3) {
                    showError('Too many failed attempts. Account temporarily locked for security.');
                    document.getElementById('loginAttempts').style.display = 'block';
                    document.getElementById('attemptsText').textContent =
                        'Multiple failed login attempts detected. Please wait 15 minutes before trying again.';
                } else {
                    showError(error.message || 'Login failed. Please check your credentials.');
                }

                // Log failed admin login attempt
                console.warn('Admin login failed:', {
                    email: email,
                    attempts: loginAttempts,
                    time: new Date().toISOString(),
                    error: error.message
                });

            } finally {
                showLoading(false);
            }
        });

        // Enhanced security: Clear form on page unload
        window.addEventListener('beforeunload', function() {
            document.getElementById('adminEmail').value = '';
            document.getElementById('adminPassword').value = '';
        });

        // Prevent right-click context menu for additional security
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // Disable F12 and other developer tools shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'U')) {
                e.preventDefault();
            }
        });
    </script>
</body>

</html>
