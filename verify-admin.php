<?php
/**
 * Admin User Verification Script
 * This script checks if the admin user exists and creates one if it doesn't
 */

// Include the API configuration
require_once __DIR__ . '/api/config/config.php';

try {
    // Create database connection using API config
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    
    echo "<h2>Database Connection: SUCCESS</h2>";
    echo "<p>Connected to database: " . DB_NAME . "</p>";
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT id, email, user_type, status, email_verified FROM users WHERE user_type = 'admin'");
    $stmt->execute();
    $adminUsers = $stmt->fetchAll();
    
    echo "<h3>Existing Admin Users:</h3>";
    if (empty($adminUsers)) {
        echo "<p style='color: red;'>No admin users found!</p>";
        
        // Create admin user
        echo "<h3>Creating Admin User...</h3>";
        
        $adminEmail = '<EMAIL>';
        $adminPassword = 'admin123';
        $passwordHash = password_hash($adminPassword, PASSWORD_DEFAULT);
        
        $insertStmt = $pdo->prepare("
            INSERT INTO users (email, password_hash, user_type, first_name, last_name, status, email_verified) 
            VALUES (?, ?, 'admin', 'System', 'Administrator', 'active', 1)
        ");
        
        if ($insertStmt->execute([$adminEmail, $passwordHash])) {
            $adminId = $pdo->lastInsertId();
            echo "<p style='color: green;'>Admin user created successfully!</p>";
            echo "<p>Email: $adminEmail</p>";
            echo "<p>Password: $adminPassword</p>";
            echo "<p>User ID: $adminId</p>";
            
            // Create admin profile
            $profileStmt = $pdo->prepare("
                INSERT INTO admin_profiles (user_id, role, permissions) 
                VALUES (?, 'super_admin', '{\"all\": true}')
            ");
            
            if ($profileStmt->execute([$adminId])) {
                echo "<p style='color: green;'>Admin profile created successfully!</p>";
            } else {
                echo "<p style='color: orange;'>Admin user created but profile creation failed.</p>";
            }
        } else {
            echo "<p style='color: red;'>Failed to create admin user!</p>";
        }
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Email</th><th>Type</th><th>Status</th><th>Email Verified</th></tr>";
        foreach ($adminUsers as $admin) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($admin['id']) . "</td>";
            echo "<td>" . htmlspecialchars($admin['email']) . "</td>";
            echo "<td>" . htmlspecialchars($admin['user_type']) . "</td>";
            echo "<td>" . htmlspecialchars($admin['status']) . "</td>";
            echo "<td>" . ($admin['email_verified'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test password verification
    echo "<h3>Testing Password Verification:</h3>";
    $testStmt = $pdo->prepare("SELECT id, email, password_hash FROM users WHERE email = '<EMAIL>'");
    $testStmt->execute();
    $testUser = $testStmt->fetch();
    
    if ($testUser) {
        $testPassword = 'admin123';
        $isValid = password_verify($testPassword, $testUser['password_hash']);
        echo "<p>Password verification for 'admin123': " . ($isValid ? '<span style="color: green;">VALID</span>' : '<span style="color: red;">INVALID</span>') . "</p>";
        
        if (!$isValid) {
            echo "<p style='color: orange;'>Updating password hash...</p>";
            $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
            $updateStmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE id = ?");
            if ($updateStmt->execute([$newHash, $testUser['id']])) {
                echo "<p style='color: green;'>Password hash updated successfully!</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>Admin user not found for password test!</p>";
    }
    
    // Test API endpoint
    echo "<h3>Testing API Endpoint:</h3>";
    echo "<p>You can now test the admin login at: <a href='admin-login.html'>admin-login.html</a></p>";
    echo "<p>Or use the test page: <a href='test-admin-login.html'>test-admin-login.html</a></p>";
    
} catch (PDOException $e) {
    echo "<h2 style='color: red;'>Database Connection Failed!</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database configuration in api/config/config.php</p>";
    
    echo "<h3>Current Configuration:</h3>";
    echo "<ul>";
    echo "<li>Host: " . DB_HOST . "</li>";
    echo "<li>Database: " . DB_NAME . "</li>";
    echo "<li>User: " . DB_USER . "</li>";
    echo "<li>Charset: " . DB_CHARSET . "</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>Error!</h2>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Admin User Verification</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Brick & Click - Admin User Verification</h1>
    <p>This script verifies and creates the admin user for the system.</p>
    <hr>
</body>
</html>
