<?php
/**
 * Payment Controller
 * Handles payment processing and transactions
 */

class PaymentController {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get project payments
     */
    public function getProjectPayments($projectId, $userId, $userType) {
        // Verify user has access to project
        $accessSql = $userType === 'customer' 
            ? "SELECT id FROM projects WHERE id = :project_id AND customer_id = :user_id"
            : "SELECT p.id FROM projects p JOIN contractor_profiles cp ON p.contractor_id = cp.id WHERE p.id = :project_id AND cp.user_id = :user_id";
        
        $project = $this->db->fetchOne($accessSql, [':project_id' => $projectId, ':user_id' => $userId]);
        
        if (!$project) {
            throw new Exception("Project not found or access denied");
        }
        
        $sql = "SELECT pp.*, pt.transaction_id, pt.payment_method, pt.status as transaction_status,
                pt.gateway_response, pt.created_at as transaction_date
                FROM project_payments pp
                LEFT JOIN payment_transactions pt ON pp.id = pt.payment_id
                WHERE pp.project_id = :project_id
                ORDER BY pp.due_date";
        
        return $this->db->fetchAll($sql, [':project_id' => $projectId]);
    }
    
    /**
     * Process payment
     */
    public function processPayment($paymentId, $customerId, $paymentMethod, $paymentData) {
        // Get payment details
        $payment = $this->db->fetchOne(
            "SELECT pp.*, p.customer_id, p.title as project_title
             FROM project_payments pp
             JOIN projects p ON pp.project_id = p.id
             WHERE pp.id = :payment_id AND p.customer_id = :customer_id",
            [':payment_id' => $paymentId, ':customer_id' => $customerId]
        );
        
        if (!$payment) {
            throw new Exception("Payment not found or access denied");
        }
        
        if ($payment['status'] !== 'pending') {
            throw new Exception("Payment is not pending");
        }
        
        try {
            $this->db->beginTransaction();
            
            // Create payment transaction record
            $transactionId = $this->generateTransactionId();
            
            $sql = "INSERT INTO payment_transactions (payment_id, customer_id, transaction_id, 
                    amount, payment_method, status, gateway_data) 
                    VALUES (:payment_id, :customer_id, :transaction_id, 
                    :amount, :payment_method, 'processing', :gateway_data)";
            
            $transactionDbId = $this->db->insert($sql, [
                ':payment_id' => $paymentId,
                ':customer_id' => $customerId,
                ':transaction_id' => $transactionId,
                ':amount' => $payment['amount'],
                ':payment_method' => $paymentMethod,
                ':gateway_data' => json_encode($paymentData)
            ]);
            
            // Process payment based on method
            $gatewayResponse = $this->processPaymentGateway($paymentMethod, $payment['amount'], $paymentData, $transactionId);
            
            if ($gatewayResponse['success']) {
                // Update transaction status
                $this->db->update(
                    "UPDATE payment_transactions SET status = 'completed', gateway_response = :response, completed_at = NOW() WHERE id = :id",
                    [':response' => json_encode($gatewayResponse), ':id' => $transactionDbId]
                );
                
                // Update payment status
                $this->db->update(
                    "UPDATE project_payments SET status = 'paid', paid_at = NOW() WHERE id = :id",
                    [':id' => $paymentId]
                );
                
                // Log activity
                log_activity($customerId, 'payment_completed', 'payment', $paymentId, "Payment completed for {$payment['project_title']}");
                
                $this->db->commit();
                
                return [
                    'transaction_id' => $transactionId,
                    'status' => 'completed',
                    'gateway_response' => $gatewayResponse
                ];
            } else {
                // Update transaction status to failed
                $this->db->update(
                    "UPDATE payment_transactions SET status = 'failed', gateway_response = :response WHERE id = :id",
                    [':response' => json_encode($gatewayResponse), ':id' => $transactionDbId]
                );
                
                $this->db->commit();
                
                throw new Exception($gatewayResponse['error'] ?? 'Payment processing failed');
            }
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Process payment through gateway
     */
    private function processPaymentGateway($paymentMethod, $amount, $paymentData, $transactionId) {
        switch ($paymentMethod) {
            case 'card':
                return $this->processCardPayment($amount, $paymentData, $transactionId);
            case 'bank_transfer':
                return $this->processBankTransfer($amount, $paymentData, $transactionId);
            case 'mobile_payment':
                return $this->processMobilePayment($amount, $paymentData, $transactionId);
            default:
                return ['success' => false, 'error' => 'Unsupported payment method'];
        }
    }
    
    /**
     * Process card payment (integrate with payment gateway)
     */
    private function processCardPayment($amount, $paymentData, $transactionId) {
        // This is a placeholder implementation
        // In a real application, you would integrate with payment gateways like:
        // - PayHere (Sri Lankan payment gateway)
        // - Stripe
        // - PayPal
        // - Local bank gateways
        
        // Simulate payment processing
        if (isset($paymentData['card_number']) && isset($paymentData['cvv']) && isset($paymentData['expiry'])) {
            // Simulate successful payment (90% success rate)
            $success = rand(1, 10) <= 9;
            
            if ($success) {
                return [
                    'success' => true,
                    'gateway_transaction_id' => 'TXN_' . time() . '_' . rand(1000, 9999),
                    'gateway' => 'payhere',
                    'message' => 'Payment processed successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Payment declined by bank',
                    'gateway' => 'payhere'
                ];
            }
        } else {
            return [
                'success' => false,
                'error' => 'Invalid card details'
            ];
        }
    }
    
    /**
     * Process bank transfer
     */
    private function processBankTransfer($amount, $paymentData, $transactionId) {
        // For bank transfers, we typically mark as pending and require manual verification
        return [
            'success' => true,
            'gateway_transaction_id' => 'BT_' . time() . '_' . rand(1000, 9999),
            'gateway' => 'bank_transfer',
            'message' => 'Bank transfer initiated. Please complete the transfer and upload proof.',
            'requires_verification' => true
        ];
    }
    
    /**
     * Process mobile payment (Dialog eZ Cash, Mobitel mCash, etc.)
     */
    private function processMobilePayment($amount, $paymentData, $transactionId) {
        // Integrate with mobile payment providers
        if (isset($paymentData['mobile_number']) && isset($paymentData['provider'])) {
            // Simulate mobile payment processing
            $success = rand(1, 10) <= 8; // 80% success rate
            
            if ($success) {
                return [
                    'success' => true,
                    'gateway_transaction_id' => 'MP_' . time() . '_' . rand(1000, 9999),
                    'gateway' => $paymentData['provider'],
                    'message' => 'Mobile payment processed successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Mobile payment failed. Please check your balance.',
                    'gateway' => $paymentData['provider']
                ];
            }
        } else {
            return [
                'success' => false,
                'error' => 'Invalid mobile payment details'
            ];
        }
    }
    
    /**
     * Generate unique transaction ID
     */
    private function generateTransactionId() {
        return 'BC_' . date('Ymd') . '_' . strtoupper(uniqid());
    }
    
    /**
     * Get payment history for customer
     */
    public function getCustomerPaymentHistory($customerId, $page = 1, $limit = 20) {
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT pp.*, p.title as project_title, pt.transaction_id, pt.payment_method,
                pt.status as transaction_status, pt.completed_at
                FROM project_payments pp
                JOIN projects p ON pp.project_id = p.id
                LEFT JOIN payment_transactions pt ON pp.id = pt.payment_id
                WHERE p.customer_id = :customer_id
                ORDER BY pp.created_at DESC
                LIMIT :limit OFFSET :offset";
        
        $payments = $this->db->fetchAll($sql, [
            ':customer_id' => $customerId,
            ':limit' => $limit,
            ':offset' => $offset
        ]);
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM project_payments pp 
                     JOIN projects p ON pp.project_id = p.id 
                     WHERE p.customer_id = :customer_id";
        $totalResult = $this->db->fetchOne($countSql, [':customer_id' => $customerId]);
        $total = $totalResult['total'];
        
        return [
            'payments' => $payments,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_items' => $total,
                'items_per_page' => $limit
            ]
        ];
    }
    
    /**
     * Get payment statistics
     */
    public function getPaymentStatistics($contractorId = null, $customerId = null) {
        $conditions = [];
        $params = [];
        
        if ($contractorId) {
            $conditions[] = "p.contractor_id = :contractor_id";
            $params[':contractor_id'] = $contractorId;
        }
        
        if ($customerId) {
            $conditions[] = "p.customer_id = :customer_id";
            $params[':customer_id'] = $customerId;
        }
        
        $whereClause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
        
        $sql = "SELECT 
                COUNT(*) as total_payments,
                COUNT(CASE WHEN pp.status = 'paid' THEN 1 END) as completed_payments,
                COUNT(CASE WHEN pp.status = 'pending' THEN 1 END) as pending_payments,
                COUNT(CASE WHEN pp.status = 'overdue' THEN 1 END) as overdue_payments,
                SUM(pp.amount) as total_amount,
                SUM(CASE WHEN pp.status = 'paid' THEN pp.amount ELSE 0 END) as paid_amount,
                SUM(CASE WHEN pp.status = 'pending' THEN pp.amount ELSE 0 END) as pending_amount
                FROM project_payments pp
                JOIN projects p ON pp.project_id = p.id
                $whereClause";
        
        return $this->db->fetchOne($sql, $params);
    }
    
    /**
     * Mark payment as overdue
     */
    public function markOverduePayments() {
        $sql = "UPDATE project_payments 
                SET status = 'overdue' 
                WHERE status = 'pending' 
                AND due_date < CURDATE()";
        
        $affected = $this->db->update($sql);
        
        // Send notifications for overdue payments
        if ($affected > 0) {
            $this->sendOverdueNotifications();
        }
        
        return $affected;
    }
    
    /**
     * Send overdue payment notifications
     */
    private function sendOverdueNotifications() {
        $sql = "SELECT pp.*, p.customer_id, p.title as project_title
                FROM project_payments pp
                JOIN projects p ON pp.project_id = p.id
                WHERE pp.status = 'overdue'
                AND pp.notified_at IS NULL";
        
        $overduePayments = $this->db->fetchAll($sql);
        
        $notificationController = new NotificationController();
        
        foreach ($overduePayments as $payment) {
            // Send notification to customer
            NotificationHelper::paymentDue(
                $payment['customer_id'],
                number_format($payment['amount'], 2),
                $payment['due_date'],
                $payment['project_id']
            );
            
            // Mark as notified
            $this->db->update(
                "UPDATE project_payments SET notified_at = NOW() WHERE id = :id",
                [':id' => $payment['id']]
            );
        }
    }
    
    /**
     * Process refund
     */
    public function processRefund($transactionId, $adminId, $reason, $amount = null) {
        $transaction = $this->db->fetchOne(
            "SELECT pt.*, pp.amount as payment_amount
             FROM payment_transactions pt
             JOIN project_payments pp ON pt.payment_id = pp.id
             WHERE pt.transaction_id = :transaction_id",
            [':transaction_id' => $transactionId]
        );
        
        if (!$transaction) {
            throw new Exception("Transaction not found");
        }
        
        if ($transaction['status'] !== 'completed') {
            throw new Exception("Can only refund completed transactions");
        }
        
        $refundAmount = $amount ?? $transaction['payment_amount'];
        
        if ($refundAmount > $transaction['payment_amount']) {
            throw new Exception("Refund amount cannot exceed payment amount");
        }
        
        try {
            $this->db->beginTransaction();
            
            // Create refund record
            $refundId = $this->db->insert(
                "INSERT INTO payment_refunds (transaction_id, refund_amount, reason, processed_by, status) 
                 VALUES (:transaction_id, :refund_amount, :reason, :processed_by, 'processing')",
                [
                    ':transaction_id' => $transaction['id'],
                    ':refund_amount' => $refundAmount,
                    ':reason' => $reason,
                    ':processed_by' => $adminId
                ]
            );
            
            // Process refund through gateway (placeholder)
            $refundResponse = $this->processGatewayRefund($transaction, $refundAmount);
            
            if ($refundResponse['success']) {
                // Update refund status
                $this->db->update(
                    "UPDATE payment_refunds SET status = 'completed', gateway_response = :response, completed_at = NOW() WHERE id = :id",
                    [':response' => json_encode($refundResponse), ':id' => $refundId]
                );
                
                // If full refund, update payment status
                if ($refundAmount == $transaction['payment_amount']) {
                    $this->db->update(
                        "UPDATE project_payments SET status = 'refunded' WHERE id = :id",
                        [':id' => $transaction['payment_id']]
                    );
                }
                
                $this->db->commit();
                
                // Log activity
                log_activity($adminId, 'refund_processed', 'payment_refund', $refundId, "Refund processed: LKR {$refundAmount}");
                
                return ['refund_id' => $refundId, 'status' => 'completed'];
            } else {
                // Update refund status to failed
                $this->db->update(
                    "UPDATE payment_refunds SET status = 'failed', gateway_response = :response WHERE id = :id",
                    [':response' => json_encode($refundResponse), ':id' => $refundId]
                );
                
                $this->db->commit();
                
                throw new Exception($refundResponse['error'] ?? 'Refund processing failed');
            }
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Process gateway refund (placeholder)
     */
    private function processGatewayRefund($transaction, $amount) {
        // Placeholder implementation
        // In real application, integrate with payment gateway refund APIs
        
        return [
            'success' => true,
            'refund_id' => 'REF_' . time() . '_' . rand(1000, 9999),
            'message' => 'Refund processed successfully'
        ];
    }
}
?>
