<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Sign Up - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Open Sans', sans-serif;
            overflow-x: hidden;
        }
        
        .signup-selection-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 40px 0;
            position: relative;
        }
        
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .content-wrapper {
            position: relative;
            z-index: 2;
            width: 100%;
        }
        
        .main-title {
            text-align: center;
            color: white;
            margin-bottom: 60px;
        }
        
        .main-title h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .main-title p {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .choice-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 50px 40px;
            text-align: center;
            height: 100%;
            transition: all 0.4s ease;
            border: 2px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }
        
        .choice-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.6s;
        }
        
        .choice-card:hover::before {
            left: 100%;
        }
        
        .choice-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 30px 60px rgba(0,0,0,0.2);
            border-color: rgba(255, 255, 255, 0.4);
        }
        
        .choice-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            position: relative;
            z-index: 2;
        }
        
        .customer-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }
        
        .contractor-icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            box-shadow: 0 15px 30px rgba(245, 87, 108, 0.4);
        }
        
        .choice-card h3 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #2d3748;
        }
        
        .choice-card p {
            font-size: 1.1rem;
            color: #718096;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .feature-list {
            text-align: left;
            margin: 30px 0;
            padding: 0;
            list-style: none;
        }
        
        .feature-list li {
            padding: 12px 0;
            position: relative;
            padding-left: 35px;
            font-size: 1rem;
            color: #4a5568;
        }
        
        .feature-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            top: 12px;
            width: 25px;
            height: 25px;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .btn-choice {
            padding: 18px 40px;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            border: none;
            width: 100%;
            position: relative;
            overflow: hidden;
            z-index: 2;
        }
        
        .btn-customer {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-customer:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .btn-contractor {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            box-shadow: 0 10px 25px rgba(245, 87, 108, 0.3);
        }
        
        .btn-contractor:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(245, 87, 108, 0.4);
            color: white;
        }
        
        .bottom-links {
            text-align: center;
            margin-top: 50px;
        }
        
        .bottom-links a {
            color: white;
            text-decoration: none;
            font-weight: 600;
            margin: 0 20px;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
        }
        
        .bottom-links a:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            color: white;
        }
        
        .stats-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            text-align: center;
        }
        
        .stats-section h4 {
            color: white;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .stats-row {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
        }
        
        .stat-item {
            color: white;
            margin: 10px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
    </style>
</head>

<body>
    <div class="signup-selection-container">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
        
        <div class="content-wrapper">
            <div class="container">
                <div class="main-title">
                    <h1 class="wow fadeInDown">Join Brick & Click</h1>
                    <p class="wow fadeInUp" data-wow-delay="0.2s">Choose your path to transform the construction industry in Sri Lanka</p>
                </div>
                
                <!-- Stats Section -->
                <div class="stats-section wow fadeInUp" data-wow-delay="0.3s">
                    <h4>Trusted by Thousands Across Sri Lanka</h4>
                    <div class="stats-row">
                        <div class="stat-item">
                            <span class="stat-number">500+</span>
                            <span class="stat-label">Verified Contractors</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">2,000+</span>
                            <span class="stat-label">Happy Customers</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">1,500+</span>
                            <span class="stat-label">Projects Completed</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">25</span>
                            <span class="stat-label">Districts Covered</span>
                        </div>
                    </div>
                </div>
                
                <div class="row g-5 justify-content-center">
                    <div class="col-lg-5 col-md-6">
                        <div class="choice-card wow fadeInLeft" data-wow-delay="0.4s">
                            <div class="choice-icon customer-icon">
                                <i class="fa fa-home"></i>
                            </div>
                            <h3>I'm a Customer</h3>
                            <p>Looking for reliable contractors for your construction project? Find verified professionals and get your dream project done right.</p>
                            
                            <ul class="feature-list">
                                <li>Browse 500+ verified CIDA contractors</li>
                                <li>Compare quotes from multiple professionals</li>
                                <li>Track project progress in real-time</li>
                                <li>Secure payments and quality assurance</li>
                                <li>24/7 customer support</li>
                            </ul>
                            
                            <a href="customer-signup.html" class="btn btn-choice btn-customer">
                                <i class="fa fa-user-plus me-2"></i>Sign Up as Customer
                            </a>
                        </div>
                    </div>
                    
                    <div class="col-lg-5 col-md-6">
                        <div class="choice-card wow fadeInRight" data-wow-delay="0.6s">
                            <div class="choice-icon contractor-icon">
                                <i class="fa fa-hard-hat"></i>
                            </div>
                            <h3>I'm a Contractor</h3>
                            <p>Ready to grow your construction business? Join our verified contractor network and connect with customers across Sri Lanka.</p>
                            
                            <ul class="feature-list">
                                <li>Get CIDA verification badge</li>
                                <li>Receive high-quality project leads</li>
                                <li>Showcase your portfolio and expertise</li>
                                <li>Manage quotes and projects efficiently</li>
                                <li>Build your reputation and grow business</li>
                            </ul>
                            
                            <a href="contractor-signup.html" class="btn btn-choice btn-contractor">
                                <i class="fa fa-hard-hat me-2"></i>Sign Up as Contractor
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="bottom-links wow fadeInUp" data-wow-delay="0.8s">
                    <a href="login.html">
                        <i class="fa fa-sign-in-alt me-2"></i>Already have an account? Login
                    </a>
                    <a href="index.html">
                        <i class="fa fa-arrow-left me-2"></i>Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    
    <script>
        // Initialize WOW.js for animations
        new WOW().init();
        
        // Add interactive hover effects
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.choice-card');
            
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-15px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
            
            // Animate stats numbers
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalNumber = parseInt(stat.textContent);
                let currentNumber = 0;
                const increment = finalNumber / 50;
                
                const timer = setInterval(() => {
                    currentNumber += increment;
                    if (currentNumber >= finalNumber) {
                        stat.textContent = finalNumber + (stat.textContent.includes('+') ? '+' : '');
                        clearInterval(timer);
                    } else {
                        stat.textContent = Math.floor(currentNumber) + (stat.textContent.includes('+') ? '+' : '');
                    }
                }, 50);
            });
        });
    </script>
</body>
</html>
