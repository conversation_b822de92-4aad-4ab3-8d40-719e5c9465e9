<?php
/**
 * Authentication Class
 * Handles user authentication and session management
 */

class Auth {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Register a new user
     */
    public function register($userData) {
        // Validate required fields
        $requiredFields = ['email', 'password', 'user_type', 'first_name', 'last_name'];
        foreach ($requiredFields as $field) {
            if (empty($userData[$field])) {
                throw new Exception("Missing required field: $field");
            }
        }

        // Validate email format
        if (!filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Invalid email format");
        }

        // Validate password strength
        if (strlen($userData['password']) < PASSWORD_MIN_LENGTH) {
            throw new Exception("Password must be at least " . PASSWORD_MIN_LENGTH . " characters long");
        }

        // Check if email already exists
        $existingUser = $this->db->fetchOne(
            "SELECT id FROM users WHERE email = :email",
            [':email' => $userData['email']]
        );

        if ($existingUser) {
            throw new Exception("Email already registered");
        }

        try {
            $this->db->beginTransaction();

            // Insert user
            $userSql = "INSERT INTO users (email, password_hash, user_type, first_name, last_name, phone, preferred_language, email_verification_token)
                       VALUES (:email, :password_hash, :user_type, :first_name, :last_name, :phone, :preferred_language, :email_verification_token)";

            $emailVerificationToken = bin2hex(random_bytes(32));

            $userId = $this->db->insert($userSql, [
                ':email' => $userData['email'],
                ':password_hash' => password_hash($userData['password'], PASSWORD_DEFAULT),
                ':user_type' => $userData['user_type'],
                ':first_name' => $userData['first_name'],
                ':last_name' => $userData['last_name'],
                ':phone' => $userData['phone'] ?? null,
                ':preferred_language' => $userData['preferred_language'] ?? DEFAULT_LANGUAGE,
                ':email_verification_token' => $emailVerificationToken
            ]);

            // Insert profile based on user type
            if ($userData['user_type'] === 'customer') {
                $this->createCustomerProfile($userId, $userData);
            } elseif ($userData['user_type'] === 'contractor') {
                $this->createContractorProfile($userId, $userData);
            }

            $this->db->commit();

            // Log activity
            log_activity($userId, 'user_registered', 'user', $userId, 'User registered successfully');

            // Send verification email (implement email service)
            $this->sendVerificationEmail($userData['email'], $emailVerificationToken);

            return [
                'user_id' => $userId,
                'email_verification_token' => $emailVerificationToken,
                'message' => 'Registration successful. Please check your email for verification.'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * Login user
     */
    public function login($email, $password) {
        // Validate input
        if (empty($email) || empty($password)) {
            throw new Exception("Email and password are required");
        }

        // Get user from database
        $user = $this->db->fetchOne(
            "SELECT id, email, password_hash, user_type, first_name, last_name, status, email_verified
             FROM users WHERE email = :email",
            [':email' => $email]
        );

        if (!$user) {
            throw new Exception("Invalid email or password");
        }

        // Check password
        if (!password_verify($password, $user['password_hash'])) {
            throw new Exception("Invalid email or password");
        }

        // Check account status
        if ($user['status'] !== 'active') {
            throw new Exception("Account is not active. Please contact support.");
        }

        // Check email verification
        if (!$user['email_verified']) {
            throw new Exception("Please verify your email address before logging in.");
        }

        // Update last login
        $this->db->update(
            "UPDATE users SET last_login = NOW() WHERE id = :id",
            [':id' => $user['id']]
        );

        // Create session
        $sessionData = [
            'user_id' => $user['id'],
            'email' => $user['email'],
            'user_type' => $user['user_type'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'login_time' => time()
        ];

        $_SESSION['user'] = $sessionData;

        // Log activity
        log_activity($user['id'], 'user_login', 'user', $user['id'], 'User logged in successfully');

        // Remove password hash from response
        unset($user['password_hash']);

        return $user;
    }

    /**
     * Admin login with enhanced security
     */
    public function adminLogin($email, $password, $clientIp = 'unknown') {
        // Validate input
        if (empty($email) || empty($password)) {
            throw new Exception("Email and password are required");
        }

        // Additional validation for admin emails
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Invalid email format");
        }

        // Get user from database with admin check
        $user = $this->db->fetchOne(
            "SELECT id, email, password_hash, user_type, first_name, last_name, status, email_verified
             FROM users WHERE email = :email AND user_type = 'admin'",
            [':email' => $email]
        );

        if (!$user) {
            // Log failed admin login attempt
            error_log("Failed admin login attempt for email: $email from IP: $clientIp", 3, LOG_PATH . 'security.log');
            throw new Exception("Invalid administrator credentials");
        }

        // Check password
        if (!password_verify($password, $user['password_hash'])) {
            // Log failed admin login attempt
            error_log("Failed admin login attempt (wrong password) for email: $email from IP: $clientIp", 3, LOG_PATH . 'security.log');
            throw new Exception("Invalid administrator credentials");
        }

        // Check account status
        if ($user['status'] !== 'active') {
            error_log("Admin login attempt for inactive account: $email from IP: $clientIp", 3, LOG_PATH . 'security.log');
            throw new Exception("Administrator account is not active. Please contact support.");
        }

        // Check email verification
        if (!$user['email_verified']) {
            throw new Exception("Please verify your email address before logging in.");
        }

        // Update last login
        $this->db->update(
            "UPDATE users SET last_login = NOW() WHERE id = :id",
            [':id' => $user['id']]
        );

        // Create session with admin-specific data
        $sessionData = [
            'user_id' => $user['id'],
            'email' => $user['email'],
            'user_type' => $user['user_type'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'login_time' => time(),
            'login_ip' => $clientIp,
            'is_admin' => true
        ];

        $_SESSION['user'] = $sessionData;

        // Log successful admin login
        error_log("Successful admin login: $email from IP: $clientIp", 3, LOG_PATH . 'security.log');
        log_activity($user['id'], 'admin_login', 'user', $user['id'], "Admin logged in from IP: $clientIp");

        // Remove password hash from response
        unset($user['password_hash']);

        return $user;
    }

    /**
     * Logout user
     */
    public function logout() {
        if (isset($_SESSION['user'])) {
            $userId = $_SESSION['user']['user_id'];
            log_activity($userId, 'user_logout', 'user', $userId, 'User logged out');
        }

        session_destroy();
        return true;
    }

    /**
     * Check if user is authenticated
     */
    public function isAuthenticated() {
        return isset($_SESSION['user']) &&
               isset($_SESSION['user']['user_id']) &&
               (time() - $_SESSION['user']['login_time']) < SESSION_TIMEOUT;
    }

    /**
     * Get current user
     */
    public function getCurrentUser() {
        if (!$this->isAuthenticated()) {
            return null;
        }

        return $_SESSION['user'];
    }

    /**
     * Verify email address
     */
    public function verifyEmail($token) {
        $user = $this->db->fetchOne(
            "SELECT id, email FROM users WHERE email_verification_token = :token AND email_verified = FALSE",
            [':token' => $token]
        );

        if (!$user) {
            throw new Exception("Invalid or expired verification token");
        }

        $this->db->update(
            "UPDATE users SET email_verified = TRUE, email_verification_token = NULL, status = 'active' WHERE id = :id",
            [':id' => $user['id']]
        );

        log_activity($user['id'], 'email_verified', 'user', $user['id'], 'Email address verified');

        return true;
    }

    /**
     * Request password reset
     */
    public function requestPasswordReset($email) {
        $user = $this->db->fetchOne(
            "SELECT id FROM users WHERE email = :email AND status = 'active'",
            [':email' => $email]
        );

        if (!$user) {
            // Don't reveal if email exists or not
            return true;
        }

        $resetToken = bin2hex(random_bytes(32));
        $resetExpiry = date('Y-m-d H:i:s', strtotime('+1 hour'));

        $this->db->update(
            "UPDATE users SET password_reset_token = :token, password_reset_expires = :expires WHERE id = :id",
            [
                ':token' => $resetToken,
                ':expires' => $resetExpiry,
                ':id' => $user['id']
            ]
        );

        // Send password reset email (implement email service)
        $this->sendPasswordResetEmail($email, $resetToken);

        log_activity($user['id'], 'password_reset_requested', 'user', $user['id'], 'Password reset requested');

        return true;
    }

    /**
     * Reset password
     */
    public function resetPassword($token, $newPassword) {
        if (strlen($newPassword) < PASSWORD_MIN_LENGTH) {
            throw new Exception("Password must be at least " . PASSWORD_MIN_LENGTH . " characters long");
        }

        $user = $this->db->fetchOne(
            "SELECT id FROM users WHERE password_reset_token = :token AND password_reset_expires > NOW()",
            [':token' => $token]
        );

        if (!$user) {
            throw new Exception("Invalid or expired reset token");
        }

        $this->db->update(
            "UPDATE users SET password_hash = :password_hash, password_reset_token = NULL, password_reset_expires = NULL WHERE id = :id",
            [
                ':password_hash' => password_hash($newPassword, PASSWORD_DEFAULT),
                ':id' => $user['id']
            ]
        );

        log_activity($user['id'], 'password_reset', 'user', $user['id'], 'Password reset successfully');

        return true;
    }

    /**
     * Create customer profile
     */
    private function createCustomerProfile($userId, $userData) {
        $sql = "INSERT INTO customer_profiles (user_id, district, address) VALUES (:user_id, :district, :address)";

        $this->db->insert($sql, [
            ':user_id' => $userId,
            ':district' => $userData['district'] ?? null,
            ':address' => $userData['address'] ?? null
        ]);
    }

    /**
     * Create contractor profile
     */
    private function createContractorProfile($userId, $userData) {
        $requiredFields = ['business_name', 'years_experience', 'cida_registration_number', 'cida_grade'];
        foreach ($requiredFields as $field) {
            if (empty($userData[$field])) {
                throw new Exception("Missing required contractor field: $field");
            }
        }

        $sql = "INSERT INTO contractor_profiles (user_id, business_name, years_experience, cida_registration_number, cida_grade, business_address, description)
                VALUES (:user_id, :business_name, :years_experience, :cida_registration_number, :cida_grade, :business_address, :description)";

        $contractorId = $this->db->insert($sql, [
            ':user_id' => $userId,
            ':business_name' => $userData['business_name'],
            ':years_experience' => $userData['years_experience'],
            ':cida_registration_number' => $userData['cida_registration_number'],
            ':cida_grade' => $userData['cida_grade'],
            ':business_address' => $userData['business_address'] ?? null,
            ':description' => $userData['description'] ?? null
        ]);

        // Add services if provided
        if (!empty($userData['services']) && is_array($userData['services'])) {
            foreach ($userData['services'] as $serviceId) {
                $this->db->insert(
                    "INSERT INTO contractor_services (contractor_id, service_category_id) VALUES (:contractor_id, :service_id)",
                    [':contractor_id' => $contractorId, ':service_id' => $serviceId]
                );
            }
        }

        // Add service areas if provided
        if (!empty($userData['service_areas']) && is_array($userData['service_areas'])) {
            foreach ($userData['service_areas'] as $districtId) {
                $this->db->insert(
                    "INSERT INTO contractor_service_areas (contractor_id, district_id) VALUES (:contractor_id, :district_id)",
                    [':contractor_id' => $contractorId, ':district_id' => $districtId]
                );
            }
        }
    }

    /**
     * Send verification email (placeholder - implement with actual email service)
     */
    private function sendVerificationEmail($email, $token) {
        // TODO: Implement email sending
        // For now, just log the verification link
        $verificationLink = APP_URL . "/verify-email.php?token=" . $token;
        error_log("Verification email for $email: $verificationLink", 3, LOG_PATH . 'emails.log');
    }

    /**
     * Send password reset email (placeholder - implement with actual email service)
     */
    private function sendPasswordResetEmail($email, $token) {
        // TODO: Implement email sending
        // For now, just log the reset link
        $resetLink = APP_URL . "/reset-password.php?token=" . $token;
        error_log("Password reset email for $email: $resetLink", 3, LOG_PATH . 'emails.log');
    }
}
?>
