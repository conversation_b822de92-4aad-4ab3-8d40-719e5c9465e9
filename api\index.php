<?php
/**
 * Brick & Click API Entry Point
 * Main API router and request handler
 */

// Define API access constant
define('API_ACCESS', true);

// Include configuration
require_once __DIR__ . '/config/config.php';

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = str_replace('/arkitektur-1.0.0/api', '', $path);
$path = trim($path, '/');

// Parse path segments
$segments = explode('/', $path);
$endpoint = $segments[0] ?? '';
$action = $segments[1] ?? '';
$id = $segments[2] ?? '';

// Get request data
$input = json_decode(file_get_contents('php://input'), true) ?? [];
$data = array_merge($_GET, $_POST, $input);

// Sanitize input data
$data = sanitize_input($data);

// Rate limiting (basic implementation)
if (RATE_LIMIT_ENABLED) {
    $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $rateLimitKey = "rate_limit_$clientIp";

    if (!isset($_SESSION[$rateLimitKey])) {
        $_SESSION[$rateLimitKey] = ['count' => 0, 'reset_time' => time() + RATE_LIMIT_WINDOW];
    }

    if (time() > $_SESSION[$rateLimitKey]['reset_time']) {
        $_SESSION[$rateLimitKey] = ['count' => 0, 'reset_time' => time() + RATE_LIMIT_WINDOW];
    }

    $_SESSION[$rateLimitKey]['count']++;

    if ($_SESSION[$rateLimitKey]['count'] > RATE_LIMIT_REQUESTS) {
        error('Rate limit exceeded. Please try again later.', 429);
    }
}

// Initialize authentication
$auth = new Auth();

// Public endpoints that don't require authentication
$publicEndpoints = [
    'auth/login',
    'auth/admin-login',
    'auth/register',
    'auth/verify-email',
    'auth/forgot-password',
    'auth/reset-password',
    'contractors/search',
    'contractors/details',
    'cost-estimator',
    'districts',
    'service-categories'
];

$currentEndpoint = $endpoint . ($action ? '/' . $action : '');

// Check authentication for protected endpoints
if (!in_array($currentEndpoint, $publicEndpoints) && !$auth->isAuthenticated()) {
    error('Authentication required', HTTP_UNAUTHORIZED);
}

// Route requests
try {
    switch ($endpoint) {
        case 'auth':
            handleAuthEndpoints($method, $action, $data, $auth);
            break;

        case 'contractors':
            handleContractorEndpoints($method, $action, $id, $data, $auth);
            break;

        case 'customers':
            handleCustomerEndpoints($method, $action, $id, $data, $auth);
            break;

        case 'quotes':
            handleQuoteEndpoints($method, $action, $id, $data, $auth);
            break;

        case 'projects':
            handleProjectEndpoints($method, $action, $id, $data, $auth);
            break;

        case 'reviews':
            handleReviewEndpoints($method, $action, $id, $data, $auth);
            break;

        case 'admin':
            handleAdminEndpoints($method, $action, $id, $data, $auth);
            break;

        case 'cost-estimator':
            handleCostEstimator($method, $data);
            break;

        case 'districts':
            handleDistricts($method);
            break;

        case 'service-categories':
            handleServiceCategories($method);
            break;

        case 'upload':
            handleFileUpload($method, $data, $auth);
            break;

        case 'notifications':
            handleNotifications($method, $action, $id, $data, $auth);
            break;

        default:
            error('Endpoint not found', HTTP_NOT_FOUND);
    }
} catch (Exception $e) {
    error($e->getMessage(), HTTP_INTERNAL_SERVER_ERROR);
}

/**
 * Handle authentication endpoints
 */
function handleAuthEndpoints($method, $action, $data, $auth) {
    switch ($action) {
        case 'login':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            validate_required_fields($data, ['email', 'password']);

            $user = $auth->login($data['email'], $data['password']);
            success($user, 'Login successful');
            break;

        case 'admin-login':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            validate_required_fields($data, ['email', 'password']);

            // Enhanced security for admin login
            $user = $auth->adminLogin($data['email'], $data['password'], $_SERVER['REMOTE_ADDR'] ?? 'unknown');
            success($user, 'Admin login successful');
            break;

        case 'register':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            $result = $auth->register($data);
            success($result, 'Registration successful');
            break;

        case 'logout':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            $auth->logout();
            success(null, 'Logout successful');
            break;

        case 'verify-email':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            validate_required_fields($data, ['token']);

            $auth->verifyEmail($data['token']);
            success(null, 'Email verified successfully');
            break;

        case 'forgot-password':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            validate_required_fields($data, ['email']);

            $auth->requestPasswordReset($data['email']);
            success(null, 'Password reset email sent');
            break;

        case 'reset-password':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            validate_required_fields($data, ['token', 'password']);

            $auth->resetPassword($data['token'], $data['password']);
            success(null, 'Password reset successful');
            break;

        case 'me':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            $user = $auth->getCurrentUser();
            if (!$user) {
                error('Not authenticated', HTTP_UNAUTHORIZED);
            }

            success($user);
            break;

        default:
            error('Auth action not found', HTTP_NOT_FOUND);
    }
}

/**
 * Handle contractor endpoints
 */
function handleContractorEndpoints($method, $action, $id, $data, $auth) {
    $contractorController = new ContractorController();

    switch ($action) {
        case 'search':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            $filters = [
                'search' => $data['search'] ?? '',
                'district' => $data['district'] ?? '',
                'service_category' => $data['service_category'] ?? '',
                'min_rating' => $data['min_rating'] ?? '',
                'cida_grade' => $data['cida_grade'] ?? '',
                'min_experience' => $data['min_experience'] ?? '',
                'sort_by' => $data['sort_by'] ?? 'rating'
            ];

            $page = max(1, intval($data['page'] ?? 1));
            $limit = min(MAX_PAGE_SIZE, max(1, intval($data['limit'] ?? DEFAULT_PAGE_SIZE)));

            $result = $contractorController->search($filters, $page, $limit);
            success($result);
            break;

        case 'details':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if (!$id) {
                error('Contractor ID required', HTTP_BAD_REQUEST);
            }

            $contractor = $contractorController->getDetails($id);
            success($contractor);
            break;

        case 'profile':
            $user = $auth->getCurrentUser();
            if ($user['user_type'] !== 'contractor') {
                error('Access denied', HTTP_FORBIDDEN);
            }

            if ($method === 'GET') {
                $profile = $contractorController->getProfile($user['user_id']);
                success($profile);
            } elseif ($method === 'PUT') {
                $result = $contractorController->updateProfile($user['user_id'], $data);
                success($result, 'Profile updated successfully');
            } else {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }
            break;

        case 'portfolio':
            $user = $auth->getCurrentUser();
            if ($user['user_type'] !== 'contractor') {
                error('Access denied', HTTP_FORBIDDEN);
            }

            if ($method === 'GET') {
                $portfolio = $contractorController->getPortfolio($user['user_id']);
                success($portfolio);
            } elseif ($method === 'POST') {
                $result = $contractorController->addPortfolioItem($user['user_id'], $data);
                success($result, 'Portfolio item added successfully');
            } else {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }
            break;

        default:
            error('Contractor action not found', HTTP_NOT_FOUND);
    }
}

/**
 * Handle cost estimator
 */
function handleCostEstimator($method, $data) {
    if ($method !== 'POST') {
        error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
    }

    validate_required_fields($data, ['project_type', 'building_type', 'floor_area', 'floors', 'district']);

    $costEstimator = new CostEstimator();
    $estimate = $costEstimator->calculate($data);

    success($estimate);
}

/**
 * Handle districts endpoint
 */
function handleDistricts($method) {
    if ($method !== 'GET') {
        error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
    }

    $db = Database::getInstance();
    $districts = $db->fetchAll("SELECT * FROM districts WHERE is_active = TRUE ORDER BY name");

    success($districts);
}

/**
 * Handle service categories endpoint
 */
function handleServiceCategories($method) {
    if ($method !== 'GET') {
        error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
    }

    $db = Database::getInstance();
    $categories = $db->fetchAll("SELECT * FROM service_categories WHERE is_active = TRUE ORDER BY sort_order, name");

    success($categories);
}

/**
 * Handle file upload
 */
function handleFileUpload($method, $data, $auth) {
    if ($method !== 'POST') {
        error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
    }

    $user = $auth->getCurrentUser();
    if (!$user) {
        error('Authentication required', HTTP_UNAUTHORIZED);
    }

    $fileUploader = new FileUploader();
    $result = $fileUploader->upload($_FILES, $data['type'] ?? 'general', $user['user_id']);

    success($result, 'File uploaded successfully');
}

/**
 * Handle notifications
 */
function handleNotifications($method, $action, $id, $data, $auth) {
    $user = $auth->getCurrentUser();
    if (!$user) {
        error('Authentication required', HTTP_UNAUTHORIZED);
    }

    $notificationController = new NotificationController();

    switch ($action) {
        case '':
        case 'list':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            $page = max(1, intval($data['page'] ?? 1));
            $limit = min(50, max(1, intval($data['limit'] ?? 20)));
            $unreadOnly = isset($data['unread_only']) && $data['unread_only'] === 'true';

            $notifications = $notificationController->getNotifications($user['user_id'], $page, $limit, $unreadOnly);
            success($notifications);
            break;

        case 'mark-read':
            if ($method !== 'PUT') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if (!$id) {
                error('Notification ID required', HTTP_BAD_REQUEST);
            }

            $result = $notificationController->markAsRead($id, $user['user_id']);
            success($result, 'Notification marked as read');
            break;

        case 'mark-all-read':
            if ($method !== 'PUT') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            $result = $notificationController->markAllAsRead($user['user_id']);
            success($result, 'All notifications marked as read');
            break;

        default:
            error('Notification action not found', HTTP_NOT_FOUND);
    }
}

// Placeholder functions for other endpoints (to be implemented)
function handleCustomerEndpoints($method, $action, $id, $data, $auth) {
    error('Customer endpoints not yet implemented', HTTP_NOT_FOUND);
}

function handleQuoteEndpoints($method, $action, $id, $data, $auth) {
    $user = $auth->getCurrentUser();
    if (!$user) {
        error('Authentication required', HTTP_UNAUTHORIZED);
    }

    $quoteController = new QuoteController();

    switch ($action) {
        case 'request':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if ($user['user_type'] !== 'customer') {
                error('Only customers can create quote requests', HTTP_FORBIDDEN);
            }

            $result = $quoteController->createQuoteRequest($user['user_id'], $data);
            success($result, 'Quote request created successfully');
            break;

        case 'customer':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if ($user['user_type'] !== 'customer') {
                error('Access denied', HTTP_FORBIDDEN);
            }

            $page = max(1, intval($data['page'] ?? 1));
            $limit = min(50, max(1, intval($data['limit'] ?? 10)));

            $quotes = $quoteController->getCustomerQuotes($user['user_id'], $page, $limit);
            success($quotes);
            break;

        case 'contractor':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if ($user['user_type'] !== 'contractor') {
                error('Access denied', HTTP_FORBIDDEN);
            }

            $contractorController = new ContractorController();
            $contractorProfile = $contractorController->getContractorByUserId($user['user_id']);

            $status = $data['status'] ?? null;
            $page = max(1, intval($data['page'] ?? 1));
            $limit = min(50, max(1, intval($data['limit'] ?? 10)));

            $quotes = $quoteController->getContractorQuotes($contractorProfile['id'], $status, $page, $limit);
            success($quotes);
            break;

        case 'respond':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if ($user['user_type'] !== 'contractor') {
                error('Only contractors can respond to quotes', HTTP_FORBIDDEN);
            }

            validate_required_fields($data, ['quote_request_id']);

            $contractorController = new ContractorController();
            $contractorProfile = $contractorController->getContractorByUserId($user['user_id']);

            $result = $quoteController->submitQuoteResponse($contractorProfile['id'], $data['quote_request_id'], $data);
            success($result, 'Quote response submitted successfully');
            break;

        case 'accept':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if ($user['user_type'] !== 'customer') {
                error('Only customers can accept quotes', HTTP_FORBIDDEN);
            }

            validate_required_fields($data, ['response_id']);

            $result = $quoteController->acceptQuoteResponse($user['user_id'], $data['response_id']);
            success($result, 'Quote accepted successfully');
            break;

        case 'details':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if (!$id) {
                error('Quote response ID required', HTTP_BAD_REQUEST);
            }

            $details = $quoteController->getQuoteResponseDetails($id, $user['user_id'], $user['user_type']);
            success($details);
            break;

        default:
            error('Quote action not found', HTTP_NOT_FOUND);
    }
}

function handleProjectEndpoints($method, $action, $id, $data, $auth) {
    $user = $auth->getCurrentUser();
    if (!$user) {
        error('Authentication required', HTTP_UNAUTHORIZED);
    }

    $projectController = new ProjectController();

    switch ($action) {
        case 'customer':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if ($user['user_type'] !== 'customer') {
                error('Access denied', HTTP_FORBIDDEN);
            }

            $status = $data['status'] ?? null;
            $page = max(1, intval($data['page'] ?? 1));
            $limit = min(50, max(1, intval($data['limit'] ?? 10)));

            $projects = $projectController->getCustomerProjects($user['user_id'], $status, $page, $limit);
            success($projects);
            break;

        case 'contractor':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if ($user['user_type'] !== 'contractor') {
                error('Access denied', HTTP_FORBIDDEN);
            }

            $contractorController = new ContractorController();
            $contractorProfile = $contractorController->getContractorByUserId($user['user_id']);

            $status = $data['status'] ?? null;
            $page = max(1, intval($data['page'] ?? 1));
            $limit = min(50, max(1, intval($data['limit'] ?? 10)));

            $projects = $projectController->getContractorProjects($contractorProfile['id'], $status, $page, $limit);
            success($projects);
            break;

        case 'details':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if (!$id) {
                error('Project ID required', HTTP_BAD_REQUEST);
            }

            $details = $projectController->getProjectDetails($id, $user['user_id'], $user['user_type']);
            success($details);
            break;

        case 'milestone':
            if ($method !== 'PUT') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if ($user['user_type'] !== 'contractor') {
                error('Only contractors can update milestones', HTTP_FORBIDDEN);
            }

            if (!$id) {
                error('Milestone ID required', HTTP_BAD_REQUEST);
            }

            validate_required_fields($data, ['status']);

            $result = $projectController->updateMilestoneStatus($id, $user['user_id'], $data['status'], $data['notes'] ?? null);
            success($result, 'Milestone updated successfully');
            break;

        case 'update':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if (!$id) {
                error('Project ID required', HTTP_BAD_REQUEST);
            }

            $result = $projectController->addProjectUpdate($id, $user['user_id'], $user['user_type'], $data);
            success($result, 'Project update added successfully');
            break;

        default:
            error('Project action not found', HTTP_NOT_FOUND);
    }
}

function handleReviewEndpoints($method, $action, $id, $data, $auth) {
    $user = $auth->getCurrentUser();
    if (!$user) {
        error('Authentication required', HTTP_UNAUTHORIZED);
    }

    $reviewController = new ReviewController();

    switch ($action) {
        case 'submit':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if ($user['user_type'] !== 'customer') {
                error('Only customers can submit reviews', HTTP_FORBIDDEN);
            }

            $result = $reviewController->submitReview($user['user_id'], $data);
            success($result, 'Review submitted successfully');
            break;

        case 'contractor':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if (!$id) {
                error('Contractor ID required', HTTP_BAD_REQUEST);
            }

            $page = max(1, intval($data['page'] ?? 1));
            $limit = min(50, max(1, intval($data['limit'] ?? 10)));

            $reviews = $reviewController->getContractorReviews($id, $page, $limit);
            success($reviews);
            break;

        case 'customer':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if ($user['user_type'] !== 'customer') {
                error('Access denied', HTTP_FORBIDDEN);
            }

            $page = max(1, intval($data['page'] ?? 1));
            $limit = min(50, max(1, intval($data['limit'] ?? 10)));

            $reviews = $reviewController->getCustomerReviews($user['user_id'], $page, $limit);
            success($reviews);
            break;

        case 'respond':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if ($user['user_type'] !== 'contractor') {
                error('Only contractors can respond to reviews', HTTP_FORBIDDEN);
            }

            if (!$id) {
                error('Review ID required', HTTP_BAD_REQUEST);
            }

            validate_required_fields($data, ['response_text']);

            $result = $reviewController->respondToReview($id, $user['user_id'], $data['response_text']);
            success($result, 'Review response added successfully');
            break;

        case 'report':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if (!$id) {
                error('Review ID required', HTTP_BAD_REQUEST);
            }

            validate_required_fields($data, ['reason']);

            $result = $reviewController->reportReview($id, $user['user_id'], $data['reason']);
            success($result, 'Review reported successfully');
            break;

        case 'statistics':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if ($user['user_type'] !== 'admin') {
                error('Admin access required', HTTP_FORBIDDEN);
            }

            $stats = $reviewController->getReviewStatistics();
            success($stats);
            break;

        default:
            error('Review action not found', HTTP_NOT_FOUND);
    }
}

function handleAdminEndpoints($method, $action, $id, $data, $auth) {
    $user = $auth->getCurrentUser();
    if (!$user || $user['user_type'] !== 'admin') {
        error('Admin access required', HTTP_FORBIDDEN);
    }

    $adminController = new AdminController();

    switch ($action) {
        case 'verifications':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            $page = max(1, intval($data['page'] ?? 1));
            $limit = min(50, max(1, intval($data['limit'] ?? 20)));

            $verifications = $adminController->getPendingVerifications($page, $limit);
            success($verifications);
            break;

        case 'verification-details':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if (!$id) {
                error('Contractor ID required', HTTP_BAD_REQUEST);
            }

            $details = $adminController->getVerificationDetails($id);
            success($details);
            break;

        case 'approve-contractor':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if (!$id) {
                error('Contractor ID required', HTTP_BAD_REQUEST);
            }

            $result = $adminController->approveContractor($id, $user['user_id'], $data['notes'] ?? null);
            success($result, 'Contractor approved successfully');
            break;

        case 'reject-contractor':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if (!$id) {
                error('Contractor ID required', HTTP_BAD_REQUEST);
            }

            validate_required_fields($data, ['reason']);

            $result = $adminController->rejectContractor($id, $user['user_id'], $data['reason']);
            success($result, 'Contractor rejected successfully');
            break;

        case 'statistics':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            $stats = $adminController->getSystemStatistics();
            success($stats);
            break;

        case 'activities':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            $limit = min(100, max(1, intval($data['limit'] ?? 50)));
            $activities = $adminController->getRecentActivities($limit);
            success($activities);
            break;

        case 'users':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            $userType = $data['user_type'] ?? null;
            $status = $data['status'] ?? null;
            $page = max(1, intval($data['page'] ?? 1));
            $limit = min(50, max(1, intval($data['limit'] ?? 20)));

            $users = $adminController->getUserManagement($userType, $status, $page, $limit);
            success($users);
            break;

        case 'update-user-status':
            if ($method !== 'PUT') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if (!$id) {
                error('User ID required', HTTP_BAD_REQUEST);
            }

            validate_required_fields($data, ['status']);

            $result = $adminController->updateUserStatus($id, $user['user_id'], $data['status'], $data['reason'] ?? null);
            success($result, 'User status updated successfully');
            break;

        case 'moderate-review':
            if ($method !== 'POST') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            if (!$id) {
                error('Review ID required', HTTP_BAD_REQUEST);
            }

            validate_required_fields($data, ['action']);

            $reviewController = new ReviewController();
            $result = $reviewController->moderateReview($id, $user['user_id'], $data['action'], $data['reason'] ?? null);
            success($result, 'Review moderated successfully');
            break;

        case 'content-moderation':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            $contentType = $data['content_type'] ?? null;
            $page = max(1, intval($data['page'] ?? 1));
            $limit = min(50, max(1, intval($data['limit'] ?? 20)));

            $content = $adminController->getContentModerationQueue($contentType, $page, $limit);
            success($content);
            break;

        case 'reports':
            if ($method !== 'GET') {
                error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
            }

            validate_required_fields($data, ['report_type']);

            $startDate = $data['start_date'] ?? null;
            $endDate = $data['end_date'] ?? null;

            $report = $adminController->generateSystemReport($data['report_type'], $startDate, $endDate);
            success($report);
            break;

        default:
            error('Admin action not found', HTTP_NOT_FOUND);
    }
}

?>
